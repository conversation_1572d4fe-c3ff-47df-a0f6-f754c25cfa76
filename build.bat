@echo off
chcp 65001
echo 🚀 OBS去重工具打包
echo ==========================================

echo 🧹 清理旧文件...
if exist build rmdir /s /q build
if exist dist rmdir /s /q dist
if exist __pycache__ rmdir /s /q __pycache__

echo 🔨 开始打包...
pyinstaller --onefile --windowed --icon=obs2.ico --name=OBS去重工具 --clean --noconfirm --add-data "obs2.ico;." --add-data "obs3.ico;." --add-data "zuozhe.png;." --add-data "platforms;platforms" --add-data "saved_colors;saved_colors" --add-data "压缩.lua;." --add-data "增益.lua;." --add-data "随机音频播放大小.lua;." --add-data "插件去重功能说明.md;." --hidden-import "PyQt5.QtCore" --hidden-import "PyQt5.QtGui" --hidden-import "PyQt5.QtWidgets" --hidden-import "websockets" --hidden-import "websocket" --hidden-import "asyncio" --hidden-import "webbrowser" main.py

echo.
if exist "dist\OBS去重工具.exe" (
    echo ✅ 打包成功！
    echo 📁 文件位置: dist\OBS去重工具.exe
    for %%A in ("dist\OBS去重工具.exe") do echo 📏 文件大小: %%~zA 字节
    echo 🖼️ 已包含图标和资源文件
) else (
    echo ❌ 打包失败
)

echo.
echo 打包完成
pause
