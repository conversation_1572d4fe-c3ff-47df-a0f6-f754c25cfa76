# 🚀 OBS去重工具打包说明

## 📋 打包前准备

### 1. 检查必要文件
确保以下文件存在于项目根目录：

- ✅ `main.py` - 主程序入口
- ✅ `main_module.py` - 主要功能模块
- ✅ `obs2.ico` - 程序图标（任务栏和程序左上角）
- ✅ `obs3.ico` - 备用图标
- ✅ `zuozhe.png` - 作者二维码图片
- ✅ `platforms/` - Qt平台插件目录
- ✅ `saved_colors/` - 保存的颜色配置
- ✅ `*.lua` - Lua脚本文件

### 2. 安装必要依赖
```bash
pip install PyInstaller
pip install Cython  # 如果需要转PYD
```

## 🛠️ 打包方式

### 方式一：快速打包（推荐）
使用 `quick_build.py` 进行快速打包：

```bash
python quick_build.py
```

**特点：**
- ✅ 自动检查必要文件
- ✅ 单文件模式，便于分发
- ✅ 自动包含所有资源文件
- ✅ 包含程序图标和zuozhe.png
- ✅ 无控制台窗口
- ✅ 支持测试运行

### 方式二：完整打包流程
使用 `build_script.py` 进行完整打包：

```bash
python build_script.py
```

**特点：**
- ✅ 支持转PYD（代码保护）
- ✅ 详细的构建过程
- ✅ 自动清理临时文件
- ✅ 完整的错误处理

### 方式三：仅转PYD
如果只需要转换Python文件为PYD格式：

```bash
python convert_to_pyd.py
```

## 🖼️ 图标和资源配置

### 程序图标设置
- **任务栏图标**: `obs2.ico`
- **程序左上角图标**: `obs2.ico`
- **图标加载**: 使用 `resource_path()` 函数确保打包后正确加载

### 资源文件配置
- **作者二维码**: `zuozhe.png`
- **加载位置**: 
  - 激活对话框中的二维码显示
  - 关于对话框中的Logo显示
- **路径处理**: 使用 `resource_path()` 函数兼容开发和打包环境

## 📁 打包输出

### 文件结构
```
dist/
└── OBS去重工具.exe  # 最终可执行文件
```

### 包含的资源
- ✅ 程序图标 (obs2.ico, obs3.ico)
- ✅ 作者二维码 (zuozhe.png)
- ✅ Qt平台插件 (platforms/)
- ✅ 颜色配置 (saved_colors/)
- ✅ Lua脚本 (*.lua)
- ✅ 说明文档 (插件去重功能说明.md)
- ✅ 测试脚本 (test_*.py)

## 🔧 高级选项

### 代码保护（PYD转换）
如果需要保护源代码，可以先转换为PYD：

1. 运行 `convert_to_pyd.py`
2. 生成 `main_module.pyd`
3. 使用PYD文件进行打包

### 自定义打包参数
可以修改 `quick_build.py` 中的构建参数：

```python
build_cmd = [
    "pyinstaller",
    "--onefile",                    # 单文件模式
    "--windowed",                   # 无控制台
    "--icon=obs2.ico",             # 图标
    "--name=OBS去重工具",           # 程序名
    # ... 其他参数
]
```

## 🧪 测试验证

### 打包后测试
1. **图标显示**: 检查任务栏和程序左上角是否显示图标
2. **资源加载**: 确认zuozhe.png正确显示
3. **功能测试**: 验证所有功能正常工作
4. **文件完整性**: 检查所有必要文件是否包含

### 常见问题排查
- **图标不显示**: 检查obs2.ico文件是否存在
- **二维码不显示**: 检查zuozhe.png文件是否正确包含
- **功能异常**: 检查相关资源文件是否缺失
- **启动失败**: 检查依赖库是否正确包含

## 📊 打包结果

### 预期文件大小
- **单文件模式**: 约 50-80 MB
- **包含所有依赖**: PyQt5, websockets等
- **包含所有资源**: 图标、图片、脚本等

### 兼容性
- **操作系统**: Windows 7/8/10/11
- **架构**: x64
- **依赖**: 无需额外安装Python或其他依赖

## 🎯 使用建议

1. **推荐使用快速打包**: `quick_build.py` 适合大多数情况
2. **代码保护需求**: 使用完整打包流程转PYD
3. **测试充分**: 打包后务必进行功能测试
4. **版本管理**: 建议为每个版本创建独立的打包

## 🔄 更新流程

当需要更新程序时：
1. 修改源代码
2. 更新版本号
3. 重新运行打包脚本
4. 测试新版本功能
5. 分发新的exe文件

---

💡 **提示**: 打包过程中如遇到问题，请检查控制台输出的详细错误信息，并确保所有必要文件都存在于正确位置。
