#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OBS去重工具快速打包脚本
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def run_command(command, description):
    """运行命令"""
    print(f"🔧 {description}")
    print(f"💻 {command}")
    
    try:
        result = subprocess.run(command, shell=True, check=True)
        print(f"✅ {description} 成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} 失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 OBS去重工具快速打包")
    print("=" * 50)
    
    # 清理旧的构建文件
    print("🧹 清理旧文件...")
    for item in ["build", "dist", "__pycache__"]:
        if os.path.exists(item):
            shutil.rmtree(item)
            print(f"   删除: {item}")
    
    # 检查必要文件
    required_files = [
        "main.py",
        "main_module.py", 
        "obs2.ico",
        "zuozhe.png"
    ]
    
    print("\n📋 检查必要文件...")
    for file in required_files:
        if os.path.exists(file):
            print(f"   ✅ {file}")
        else:
            print(f"   ❌ {file} - 文件不存在！")
            return False
    
    # 构建命令
    build_cmd = [
        "pyinstaller",
        "--onefile",                    # 单文件模式
        "--windowed",                   # 无控制台窗口
        "--icon=obs2.ico",             # 程序图标
        "--name=OBS去重工具",           # 程序名称
        "--clean",                      # 清理缓存
        "--noconfirm",                 # 不确认覆盖
        
        # 添加数据文件
        "--add-data=obs2.ico;.",
        "--add-data=obs3.ico;.",
        "--add-data=zuozhe.png;.",
        "--add-data=platforms;platforms",
        "--add-data=saved_colors;saved_colors",
        "--add-data=压缩.lua;.",
        "--add-data=增益.lua;.",
        "--add-data=随机音频播放大小.lua;.",
        "--add-data=插件去重功能说明.md;.",
        
        # 隐藏导入
        "--hidden-import=PyQt5.QtCore",
        "--hidden-import=PyQt5.QtGui",
        "--hidden-import=PyQt5.QtWidgets",
        "--hidden-import=websockets",
        "--hidden-import=websocket",
        "--hidden-import=asyncio",
        "--hidden-import=json",
        "--hidden-import=webbrowser",
        
        "main.py"                       # 主程序文件
    ]
    
    # 执行构建
    print("\n🔨 开始构建...")
    cmd_str = " ".join(build_cmd)
    
    if run_command(cmd_str, "PyInstaller构建"):
        # 检查结果
        exe_path = Path("dist/OBS去重工具.exe")
        if exe_path.exists():
            file_size = exe_path.stat().st_size / (1024 * 1024)
            print(f"\n🎉 构建成功！")
            print(f"📁 文件位置: {exe_path}")
            print(f"📏 文件大小: {file_size:.1f} MB")
            print(f"🖼️ 图标: 已包含 obs2.ico")
            print(f"🖼️ 资源: 已包含 zuozhe.png")
            
            # 测试运行
            test_run = input("\n是否测试运行程序？(y/N): ").lower().strip()
            if test_run == 'y':
                print("🧪 启动程序测试...")
                try:
                    subprocess.Popen([str(exe_path)])
                    print("✅ 程序启动成功")
                except Exception as e:
                    print(f"❌ 程序启动失败: {e}")
            
            return True
        else:
            print("❌ 未找到生成的exe文件")
            return False
    else:
        print("❌ 构建失败")
        return False

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n✨ 打包完成！可执行文件已生成在 dist 目录中")
        else:
            print("\n💥 打包失败，请检查错误信息")
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断")
    except Exception as e:
        print(f"\n❌ 异常: {e}")
    
    input("\n按回车键退出...")
