# 🎛️ VST参数调节功能使用说明

## 🎉 新功能介绍

我已经为OBS VST滤镜管理器添加了完整的参数调节功能！现在你可以：

- ✅ **实时调节VST参数** - 滑块式直观调节
- ✅ **预设参数配置** - 针对你的三个插件优化
- ✅ **参数实时生效** - 调节后立即在OBS中生效
- ✅ **重置默认值** - 一键恢复推荐设置

## 🚀 使用步骤

### 第1步：选择要调节的滤镜
1. 在主界面选择媒体源
2. 在滤镜列表中**点击选中**要调节的VST滤镜
3. 点击"**调节参数**"按钮

### 第2步：参数调节窗口
参数调节窗口会显示：
- **滤镜信息**：媒体源名称、滤镜名称、类型
- **常用参数**：针对插件类型优化的主要参数
- **其他参数**：插件的所有其他可调参数

### 第3步：调节参数
- **滑块调节**：拖动滑块实时调节数值参数
- **复选框**：点击切换布尔类型参数
- **输入框**：直接输入文本类型参数
- **实时生效**：调节后参数立即在OBS中生效

## 🎛️ 各插件的预设参数

### Auburn Sounds Graillon 3-64 (音调变声器)

#### 主要参数：
- **pitch (音调偏移)**: -12 到 +12 半音
  - 0 = 原始音调
  - 正值 = 升调（更尖锐）
  - 负值 = 降调（更低沉）

- **formant (共振峰调节)**: 50% 到 150%
  - 100% = 自然声音
  - 大于100% = 更像女声
  - 小于100% = 更像男声

- **mix (干湿混合)**: 0% 到 100%
  - 0% = 完全原声
  - 100% = 完全处理后的声音
  - 50% = 原声和处理声音各一半

#### 推荐设置：
```
轻微变声：pitch = ±2, formant = 100%, mix = 80%
女声效果：pitch = +5, formant = 120%, mix = 100%
男声效果：pitch = -3, formant = 80%, mix = 100%
机器人声：pitch = -8, formant = 60%, mix = 100%
```

### TSE_808_2.0_x64 (失真效果器)

#### 主要参数：
- **drive (失真强度)**: 0% 到 100%
  - 0% = 无失真
  - 30% = 轻微温暖感
  - 70% = 明显失真效果
  - 100% = 极限失真

- **tone (音色控制)**: 0% 到 100%
  - 0% = 偏重低频
  - 50% = 平衡音色
  - 100% = 偏重高频

- **level (输出电平)**: 0% 到 100%
  - 控制最终输出音量
  - 建议保持在70-90%

#### 推荐设置：
```
轻微增色：drive = 20%, tone = 50%, level = 80%
温暖效果：drive = 40%, tone = 30%, level = 75%
明显失真：drive = 60%, tone = 60%, level = 70%
极限效果：drive = 80%, tone = 70%, level = 65%
```

### TAL-Reverb-4-64 (混响效果器)

#### 主要参数：
- **roomsize (房间大小)**: 0% 到 100%
  - 0% = 小房间（短混响）
  - 50% = 中等房间
  - 100% = 大厅效果（长混响）

- **damping (阻尼控制)**: 0% 到 100%
  - 0% = 明亮混响（高频保留）
  - 50% = 自然衰减
  - 100% = 暗淡混响（高频快速衰减）

- **mix (混响混合)**: 0% 到 100%
  - 0% = 完全干声
  - 25% = 轻微混响
  - 50% = 明显混响
  - 100% = 完全湿声

#### 推荐设置：
```
轻微空间感：roomsize = 30%, damping = 60%, mix = 15%
自然房间：roomsize = 50%, damping = 50%, mix = 25%
大厅效果：roomsize = 80%, damping = 40%, mix = 35%
梦幻效果：roomsize = 90%, damping = 20%, mix = 50%
```

## 💡 使用技巧

### 1. 参数调节顺序
建议按以下顺序调节参数：
1. **先调主要效果参数**（如pitch、drive、roomsize）
2. **再调音色参数**（如formant、tone、damping）
3. **最后调混合比例**（如mix、level）

### 2. 实时监听
- 调节参数时保持音频播放，实时听效果
- 小幅度调节，避免参数跳跃太大
- 可以边说话边调节，找到最佳效果

### 3. 参数组合
- **音调+失真**：先调音调，再加轻微失真增色
- **音调+混响**：音调变化后加混响增加空间感
- **失真+混响**：失真后的混响效果更丰富

### 4. 保存设置
- 找到满意的参数后，记录下数值
- 可以为不同场景创建不同的参数组合
- 使用"重置默认"快速回到推荐设置

## ⚠️ 注意事项

### 参数调节注意点
- **避免极端值**：除非特殊效果需要，避免参数调到最大或最小
- **音量控制**：失真和混响会影响音量，注意调节level参数
- **CPU占用**：过多的音频处理会增加CPU负担

### 常见问题解决
1. **参数不生效**：
   - 确保滤镜在OBS中是启用状态
   - 尝试关闭重新打开参数窗口

2. **声音异常**：
   - 点击"重置默认"恢复推荐设置
   - 检查是否有多个音频处理冲突

3. **调节卡顿**：
   - 减少同时调节的参数数量
   - 关闭不必要的其他程序

## 🎊 高级功能

### 快捷操作
- **刷新参数**：重新加载当前参数值
- **重置默认**：一键恢复推荐设置
- **实时预览**：调节时立即听到效果

### 扩展功能
- 支持所有VST插件的参数调节
- 自动识别参数类型（数值/布尔/文本）
- 智能参数范围设置

现在你可以精确控制每个VST插件的效果了！试试调节不同的参数组合，创造出独特的音频效果吧！
