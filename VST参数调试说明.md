# 🧪 VST参数调试功能使用说明

## 🎯 问题诊断

你遇到的"调节了参数但重新载入没有效果"的问题，可能有以下几种原因：

### 可能的问题：
1. **参数名称不匹配** - VST插件的实际参数名与我们使用的不同
2. **参数值范围错误** - 参数值的范围或格式不正确
3. **参数设置方式错误** - OBS的VST参数设置方法可能不同
4. **插件内部状态** - 插件需要特定的初始化或激活方式

## 🆕 新增调试功能

我已经为程序添加了强大的**"调试参数"**功能来帮你找出问题！

### 🔍 调试功能特色：
1. **详细参数显示** - 显示VST插件的所有实际参数
2. **多种设置方式** - 尝试不同的参数设置方法
3. **实时验证** - 设置后立即验证参数是否真的改变了
4. **快速测试** - 预设的常用参数快速测试按钮
5. **调试日志** - 详细的操作日志帮助分析问题

## 🚀 使用调试功能

### 第1步：打开调试窗口
1. **选择VST滤镜** - 在滤镜列表中选中要调试的VST滤镜
2. **点击"调试参数"** - 点击新增的调试按钮
3. **查看参数信息** - 调试窗口会显示插件的所有实际参数

### 第2步：分析当前参数
调试窗口会显示：
- **📊 参数总数** - 插件实际有多少个参数
- **🎛️ 重要参数** - pitch、formant、mix等常用参数
- **📁 路径参数** - plugin_path等配置参数
- **🧬 Chunk数据** - 插件的二进制状态数据
- **⚪ 其他参数** - 所有其他可用参数

### 第3步：测试参数设置
1. **手动测试** - 在"参数名"和"参数值"框中输入要测试的参数
2. **快速测试** - 点击预设的快速测试按钮
3. **查看日志** - 在主窗口的日志区域查看详细的设置过程
4. **验证结果** - 点击"刷新参数"查看参数是否真的改变了

## 🔧 调试过程详解

### 多种参数设置方式：
程序会自动尝试以下方式设置参数：

1. **方式1: 直接设置**
   ```
   参数名: pitch
   设置值: 2.0
   ```

2. **方式2: VST前缀**
   ```
   尝试: vst_pitch, param_pitch, pitch_value, PITCH, pitch
   ```

3. **方式3: Chunk数据** (高级)
   ```
   通过修改二进制chunk数据设置参数
   ```

### 验证机制：
- 设置参数后等待0.1秒
- 重新获取插件参数
- 比较设置前后的参数值
- 报告参数是否真的改变了

## 🧪 快速测试按钮

### Graillon参数测试：
- **pitch=2** - 测试音调升高2半音
- **pitch=-2** - 测试音调降低2半音  
- **formant=120** - 测试共振峰120%
- **mix=50** - 测试50%混合比例

### TSE808参数测试：
- **drive=50** - 测试50%失真强度
- **tone=30** - 测试30%音色控制
- **level=70** - 测试70%输出电平

### TAL-Reverb参数测试：
- **roomsize=80** - 测试80%房间大小
- **damping=40** - 测试40%阻尼控制

## 📊 诊断常见问题

### 问题1: 参数总数很少 (< 5个)
**可能原因**: 插件未完全加载
**解决方案**: 点击"重载插件"按钮

### 问题2: 没有重要参数 (pitch, formant等)
**可能原因**: 参数名称不匹配
**解决方案**: 查看"其他参数"部分，找到实际的参数名

### 问题3: 设置参数后值没有改变
**可能原因**: 参数设置方式错误
**解决方案**: 查看日志中的详细错误信息

### 问题4: Chunk数据为空
**可能原因**: 插件需要手动初始化
**解决方案**: 在OBS中双击滤镜打开插件界面

## 💡 调试技巧

### 1. 逐步调试
```
1. 先查看参数总数是否正常 (应该 > 10个)
2. 检查是否有Chunk数据
3. 测试简单参数 (如pitch=0)
4. 逐步测试复杂参数
```

### 2. 对比分析
```
1. 记录调节前的所有参数值
2. 在OBS中手动调节插件
3. 刷新参数查看哪些参数真的改变了
4. 使用实际改变的参数名进行测试
```

### 3. 日志分析
```
查看主窗口日志中的详细信息：
- "方式1成功" - 直接设置成功
- "方式2成功" - VST前缀设置成功  
- "参数验证成功" - 参数真的改变了
- "参数值不匹配" - 设置了但值不对
```

## 🔍 实际调试示例

### 示例1: Graillon插件调试
```
1. 打开调试窗口
2. 查看参数 - 发现有30个参数，但没有"pitch"
3. 在"其他参数"中发现"semitones"参数
4. 测试: 参数名="semitones", 参数值="2"
5. 查看日志 - "方式1成功"，"参数验证成功"
6. 结论: 实际参数名是"semitones"而不是"pitch"
```

### 示例2: TSE808插件调试
```
1. 打开调试窗口
2. 参数总数只有3个 - 插件未完全加载
3. 点击"重载插件"
4. 重新打开调试窗口 - 现在有15个参数
5. 测试"drive=50" - 成功
6. 结论: 需要重载插件才能正常工作
```

## ⚠️ 注意事项

### 调试安全：
- 调试过程中参数会被修改
- 建议先备份满意的设置
- 测试完成后重新调节到满意状态

### 性能考虑：
- 频繁的参数设置可能影响音频
- 建议在不录制/直播时进行调试
- 调试完成后关闭调试窗口

## 🎉 开始调试

现在你可以：

1. **选择有问题的VST滤镜**
2. **点击"调试参数"按钮**
3. **查看实际的参数信息**
4. **测试不同的参数设置方式**
5. **找出真正有效的参数名和设置方法**

通过这个调试功能，我们一定能找出为什么参数调节没有效果的原因！

试试选择一个VST滤镜，点击"调试参数"按钮，让我们一起找出问题所在吧！
