#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Cython环境
"""

import sys
import os

def test_cython():
    """测试Cython是否可用"""
    print("🔍 测试Cython环境...")
    
    try:
        import Cython
        print(f"✅ Cython版本: {Cython.__version__}")
        return True
    except ImportError as e:
        print(f"❌ Cython未安装: {e}")
        return False

def test_setuptools():
    """测试setuptools"""
    try:
        import setuptools
        print(f"✅ setuptools版本: {setuptools.__version__}")
        return True
    except ImportError as e:
        print(f"❌ setuptools未安装: {e}")
        return False

def test_compiler():
    """测试编译器"""
    try:
        from distutils import msvccompiler
        print("✅ MSVC编译器可用")
        return True
    except ImportError:
        print("⚠️ MSVC编译器可能不可用")
        return False

def check_file():
    """检查文件"""
    if os.path.exists("main_module.py"):
        size = os.path.getsize("main_module.py") / 1024
        print(f"✅ main_module.py存在 ({size:.1f} KB)")
        return True
    else:
        print("❌ main_module.py不存在")
        return False

def main():
    """主函数"""
    print("🧪 Cython环境测试")
    print("=" * 40)
    
    results = []
    results.append(test_cython())
    results.append(test_setuptools())
    results.append(test_compiler())
    results.append(check_file())
    
    print(f"\n📊 测试结果: {sum(results)}/{len(results)} 通过")
    
    if all(results):
        print("🎉 环境检查通过，可以尝试转换PYD")
        print("💡 运行命令: python setup_main.py build_ext --inplace")
    else:
        print("⚠️ 环境检查未完全通过")
        if not results[0]:
            print("   请安装Cython: pip install Cython")
        if not results[1]:
            print("   请安装setuptools: pip install setuptools")

if __name__ == "__main__":
    main()
    input("按回车键退出...")
