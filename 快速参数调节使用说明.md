# 🎛️ 快速参数调节功能使用说明

## 🎉 新功能介绍

我已经为你的VST滤镜管理器添加了**"快速调节"**功能！这是一个专门为你的三个VST插件优化的简化参数调节界面。

### 🆕 新增按钮

现在滤镜管理区域有5个按钮：
1. **刷新滤镜** - 重新获取滤镜列表
2. **快速调节** ⭐ - 新增！简化的参数调节界面
3. **高级参数** - 原来的详细参数调节
4. **分析Chunk** - Chunk数据分析
5. **删除滤镜** - 删除选中的滤镜

## 🚀 快速调节功能特色

### 🎯 针对性优化
- **Auburn Sounds Graillon** - 音调、共振峰、混合比例
- **TSE_808** - 失真强度、音色控制、输出电平
- **TAL-Reverb** - 房间大小、阻尼控制、混响混合

### 🎛️ 直观控制
- **大滑块** - 更容易操作的滑块控件
- **实时数值显示** - 滑块旁边显示当前数值
- **即时生效** - 拖动滑块时参数立即生效

### 🎨 快速预设
每个插件都有5个常用预设按钮，一键应用！

## 📊 各插件的快速调节界面

### 🎤 Auburn Sounds Graillon (音调变声器)

#### 参数控制：
- **音调偏移** (-12 到 +12 半音)
  - 0 = 原始音调
  - 正值 = 升调（更尖锐）
  - 负值 = 降调（更低沉）

- **共振峰** (50% 到 150%)
  - 100% = 自然声音
  - 大于100% = 更像女声
  - 小于100% = 更像男声

- **干湿混合** (0% 到 100%)
  - 0% = 完全原声
  - 100% = 完全处理后的声音

#### 快速预设：
- **原声** - 无变化 (0, 100%, 100%)
- **轻微变声** - 微调音调 (+2, 100%, 80%)
- **女声效果** - 升调+共振峰 (+5, 120%, 100%)
- **男声效果** - 降调+共振峰 (-3, 80%, 100%)
- **机器人声** - 大幅降调 (-8, 60%, 100%)

### 🔥 TSE_808 (失真效果器)

#### 参数控制：
- **失真强度** (0% 到 100%)
  - 0% = 无失真
  - 30% = 轻微温暖感
  - 70% = 明显失真效果

- **音色控制** (0% 到 100%)
  - 0% = 偏重低频
  - 50% = 平衡音色
  - 100% = 偏重高频

- **输出电平** (0% 到 100%)
  - 控制最终输出音量

#### 快速预设：
- **无失真** - 干净信号 (0%, 50%, 80%)
- **轻微增色** - 微失真 (20%, 50%, 80%)
- **温暖效果** - 温暖失真 (40%, 30%, 75%)
- **明显失真** - 中等失真 (60%, 60%, 70%)
- **极限效果** - 重失真 (80%, 70%, 65%)

### 🌊 TAL-Reverb (混响效果器)

#### 参数控制：
- **房间大小** (0% 到 100%)
  - 0% = 小房间（短混响）
  - 50% = 中等房间
  - 100% = 大厅效果（长混响）

- **阻尼控制** (0% 到 100%)
  - 0% = 明亮混响
  - 50% = 自然衰减
  - 100% = 暗淡混响

- **混响混合** (0% 到 100%)
  - 0% = 完全干声
  - 25% = 轻微混响
  - 100% = 完全湿声

#### 快速预设：
- **无混响** - 干声 (0%, 0%, 0%)
- **轻微空间** - 小空间感 (30%, 60%, 15%)
- **自然房间** - 房间效果 (50%, 50%, 25%)
- **大厅效果** - 大空间 (80%, 40%, 35%)
- **梦幻效果** - 特殊效果 (90%, 20%, 50%)

## 🎯 使用方法

### 第1步：选择VST滤镜
1. 在主界面选择包含VST滤镜的媒体源
2. 在滤镜列表中**点击选中**要调节的VST滤镜

### 第2步：打开快速调节
1. 点击"**快速调节**"按钮
2. 程序会根据滤镜类型自动显示对应的控制界面

### 第3步：调节参数
- **拖动滑块** - 实时调节参数值
- **查看数值** - 滑块右侧显示当前数值
- **听效果** - 参数调节后立即在OBS中生效

### 第4步：使用预设
- **点击预设按钮** - 一键应用常用设置
- **微调参数** - 在预设基础上进行微调
- **重置默认** - 恢复推荐的默认值

## 💡 使用技巧

### 1. 参数调节顺序
**建议顺序**：
1. 先使用预设找到接近的效果
2. 再微调主要参数（如音调、失真强度、房间大小）
3. 最后调节细节参数（如混合比例、音色控制）

### 2. 实时监听
- 调节参数时保持音频播放
- 边说话边调节，实时听效果
- 小幅度调节，避免参数跳跃

### 3. 预设组合
- **音调+失真**：先用Graillon调音调，再用TSE808加失真
- **音调+混响**：音调变化后加TAL混响增加空间感
- **全套处理**：音调→失真→混响的完整处理链

### 4. 场景应用
- **直播娱乐**：使用极端预设创造趣味效果
- **专业录制**：使用轻微预设进行音质增强
- **角色扮演**：组合不同预设创造角色声音

## ⚠️ 注意事项

### 参数范围
- 所有参数都有合理的范围限制
- 避免极端数值，除非需要特殊效果
- 注意音量变化，及时调节输出电平

### 性能考虑
- 同时使用多个VST插件会增加CPU负担
- 如果出现音频卡顿，减少同时使用的插件数量
- 关闭不需要的滤镜以节省资源

### 兼容性
- 快速调节功能专门为你的三个插件优化
- 其他VST插件会显示通用控制界面
- 如果参数名称不匹配，可能需要使用"高级参数"功能

## 🔄 快速调节 vs 高级参数

### 快速调节 (推荐日常使用)
- ✅ 界面简洁，操作直观
- ✅ 针对性优化，参数精选
- ✅ 预设丰富，一键应用
- ✅ 适合快速调节和实时使用

### 高级参数 (深度调节)
- ✅ 显示所有可用参数
- ✅ 支持任意VST插件
- ✅ 参数含义自动推测
- ✅ 适合精细调节和研究

## 🎊 开始使用

现在你可以：

1. **快速上手** - 使用预设按钮快速找到合适效果
2. **精细调节** - 用滑块微调参数到完美状态
3. **实时体验** - 边调节边听效果，立即生效
4. **组合使用** - 多个插件组合创造独特音效

试试选择一个VST滤镜，点击"快速调节"按钮，体验简化而强大的参数控制功能吧！
