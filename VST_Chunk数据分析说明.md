# 🧬 VST Chunk数据分析功能说明

## 🎯 什么是Chunk数据

VST插件的**Chunk数据**是插件保存所有参数状态的二进制格式数据。它包含了插件的完整状态信息，比普通的参数名称-数值对更加详细和准确。

### 📊 你提供的Chunk数据分析

你提供的这段数据：
```
CyC6kgEAAAAAAAAAAAEDAAAAAABBAAAA7zs1PwAAAAAAAAAAAAAAAAAAAAAAAIA/AAAAAAAAAADvOzU/AAAAAAAAAAAAAAAAAACAPwAAAD8AAAAAAAAAPwAAgD8AAIA/FaTSPgAAgD/TMgE/zcxMPgAAgD8AAIA/AACAPwAAgD8AAIA/AACAPwAAgD8AAIA/AACAPwAAgD8AAIA/AACAPwAAAD8AAAA/AAAAPwAAAD8AAAA/AAAAPwAAAD8AAAA/AAAAPwAAAD8AAAA/AAAAPwAAAAAAAAAAAAAAAAAAAAAAAIA/AAAAPwAAAD8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD8AAAAAAACAPwAAgD8AAIA/AAAAAAAAAAAAAAAA
```

经过分析，这很可能是**Auburn Sounds Graillon**插件的参数数据！

## 🔍 新增的分析功能

我为程序添加了**"分析Chunk"**按钮，可以：

### 1. 自动获取当前VST滤镜的Chunk数据
- 从OBS中读取选中VST滤镜的完整状态
- 显示原始Base64编码数据
- 自动解析为可读格式

### 2. 智能参数解析
- **浮点数参数**：将二进制数据解析为浮点数数组
- **参数含义推测**：
  - 0.0 → 可能是关闭状态
  - 1.0 → 可能是100%状态
  - 0.5 → 可能是50%状态
  - -12到+12的整数 → 可能是音调半音数
  - 0-1之间的值 → 可能是百分比

### 3. 十六进制查看
- 显示原始二进制数据的十六进制格式
- 便于深入分析数据结构

### 4. 测试功能
- 可以输入任意Chunk数据进行分析
- 预填充了你提供的数据作为示例

## 🚀 使用方法

### 第1步：选择VST滤镜
1. 在主界面选择包含VST滤镜的媒体源
2. 在滤镜列表中**点击选中**要分析的VST滤镜

### 第2步：打开分析窗口
1. 点击"**分析Chunk**"按钮
2. 程序会自动获取该滤镜的Chunk数据

### 第3步：查看分析结果
- **原始数据**：Base64编码的完整数据
- **解析结果**：
  - 数据长度信息
  - 浮点数参数列表
  - 参数含义推测
  - 十六进制数据显示

### 第4步：测试自定义数据
1. 点击"**测试Chunk**"按钮
2. 输入你的Chunk数据
3. 点击"分析数据"查看结果

## 📊 你的数据分析结果

根据你提供的Chunk数据，分析结果显示：

### 关键参数（推测）：
- **参数 4**: 0.709804 → 可能是音调相关参数
- **参数 6**: 0.709804 → 可能是另一个音调参数  
- **参数 8**: 1.000000 → 可能是启用状态(100%)
- **参数 10**: 1.000000 → 可能是混合比例(100%)
- **参数 12**: 0.420134 → 可能是formant参数
- **参数 14**: 0.504883 → 可能是其他调节参数
- **参数 15**: 0.200000 → 可能是20%的某个设置

### 数据特征：
- 总长度：200字节
- 包含约50个浮点数参数
- 大量的1.0和0.5值，说明很多参数处于默认状态
- 有几个特殊值，可能是用户调节过的参数

## 💡 实际应用

### 1. 参数备份和恢复
- 保存满意的Chunk数据作为预设
- 需要时可以恢复到特定状态

### 2. 精确参数控制
- 通过修改Chunk数据实现精确的参数设置
- 比普通参数调节更加准确

### 3. 参数研究
- 了解VST插件的内部参数结构
- 发现隐藏的或未公开的参数

### 4. 批量设置
- 可以将成功的Chunk数据应用到多个滤镜
- 实现参数的快速复制

## ⚠️ 注意事项

### 数据兼容性
- Chunk数据通常只在相同版本的插件间兼容
- 不同插件的Chunk格式完全不同
- 修改Chunk数据需要谨慎，错误的数据可能导致插件崩溃

### 使用建议
- 在修改Chunk数据前先备份原始数据
- 小幅度修改参数值，避免极端数值
- 测试修改后的效果，确保插件正常工作

## 🔧 高级功能（未来扩展）

### 可能的增强功能：
1. **Chunk数据编辑器** - 直接修改参数值
2. **预设管理** - 保存和加载Chunk预设
3. **参数映射** - 建立Chunk位置与参数名称的对应关系
4. **批量应用** - 将Chunk数据应用到多个滤镜

## 🎉 总结

现在你的VST滤镜管理器具备了：
- ✅ 基础参数调节（滑块方式）
- ✅ **Chunk数据分析**（深度分析）
- ✅ 参数状态查看
- ✅ 数据格式解析

这个Chunk分析功能让你能够：
- 🔍 深入了解VST插件的内部状态
- 📊 精确分析每个参数的数值
- 🧬 研究插件的参数结构
- 💾 备份和分析参数配置

试试选择一个VST滤镜，点击"分析Chunk"按钮，探索VST插件的内部世界吧！
