#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的PYD转换脚本
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

def create_simple_setup():
    """创建简化的setup文件"""
    setup_content = '''# -*- coding: utf-8 -*-
from setuptools import setup
from Cython.Build import cythonize
import os

# 设置环境变量解决编码问题
os.environ['PYTHONIOENCODING'] = 'utf-8'

# 编译器指令
compiler_directives = {
    'language_level': 3,
    'boundscheck': False,
    'wraparound': False,
}

# 编译main_module.py
setup(
    ext_modules=cythonize(
        ["main_module.py"],
        compiler_directives=compiler_directives,
        build_dir="build",
        language_level=3
    ),
    zip_safe=False,
)
'''
    
    with open("setup_simple.py", "w", encoding="utf-8") as f:
        f.write(setup_content)
    
    print("✅ 创建了 setup_simple.py")

def run_conversion():
    """运行转换"""
    print("🔄 开始转换...")
    
    # 设置环境变量
    env = os.environ.copy()
    env['PYTHONIOENCODING'] = 'utf-8'
    env['LANG'] = 'en_US.UTF-8'
    
    cmd = [sys.executable, "setup_simple.py", "build_ext", "--inplace"]
    
    try:
        print(f"💻 执行: {' '.join(cmd)}")
        result = subprocess.run(
            cmd, 
            env=env,
            capture_output=True, 
            text=True, 
            encoding='utf-8', 
            errors='replace'
        )
        
        if result.returncode == 0:
            print("✅ 转换成功")
            
            # 检查生成的文件
            pyd_files = list(Path(".").glob("*.pyd"))
            so_files = list(Path(".").glob("*.so"))
            
            if pyd_files or so_files:
                print("📁 生成的文件:")
                for f in pyd_files + so_files:
                    size = f.stat().st_size / 1024
                    print(f"   - {f} ({size:.1f} KB)")
                return True
            else:
                print("⚠️ 未找到生成的文件")
                return False
        else:
            print("❌ 转换失败")
            print(f"返回码: {result.returncode}")
            if result.stdout:
                print(f"输出: {result.stdout}")
            if result.stderr:
                print(f"错误: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 执行异常: {e}")
        return False

def cleanup():
    """清理文件"""
    print("🧹 清理临时文件...")
    
    items_to_remove = [
        "build",
        "setup_simple.py",
        "main_module.c",
    ]
    
    for item in items_to_remove:
        if os.path.exists(item):
            if os.path.isdir(item):
                shutil.rmtree(item)
            else:
                os.remove(item)
            print(f"   删除: {item}")

def main():
    """主函数"""
    print("🔄 简化PYD转换工具")
    print("=" * 40)
    
    # 检查文件
    if not os.path.exists("main_module.py"):
        print("❌ 找不到 main_module.py")
        return False
    
    # 检查Cython
    try:
        import Cython
        print(f"✅ Cython版本: {Cython.__version__}")
    except ImportError:
        print("❌ 请先安装Cython: pip install Cython")
        return False
    
    try:
        # 创建setup文件
        create_simple_setup()
        
        # 运行转换
        success = run_conversion()
        
        # 清理
        cleanup()
        
        if success:
            print("\n🎉 PYD转换成功！")
            return True
        else:
            print("\n💥 PYD转换失败")
            return False
            
    except Exception as e:
        print(f"❌ 异常: {e}")
        return False

if __name__ == "__main__":
    success = main()
    input("\n按回车键退出...")
    sys.exit(0 if success else 1)
