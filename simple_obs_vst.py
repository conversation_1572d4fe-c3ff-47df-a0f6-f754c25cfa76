#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的OBS VST滤镜管理器 - 命令行版本
功能：连接OBS、获取媒体源、管理滤镜、添加VST滤镜
"""

import json
import time
import os

try:
    import websocket
    WEBSOCKET_AVAILABLE = True
except ImportError:
    WEBSOCKET_AVAILABLE = False
    print("⚠️ websocket-client库未安装，请运行: pip install websocket-client")

class SimpleOBSVST:
    def __init__(self):
        self.ws = None
        self.is_connected = False
        self.request_id_counter = 0
        
        # VST插件配置 - 根据你的实际路径更新
        self.vst_plugins = {
            "1": {
                "name": "Auburn Sounds Graillon 3-64",
                "display_name": "Graillon 音调变声器",
                "filter_kind": "vst_filter",
                "plugin_path": r"C:\Program Files\VSTPlugins\Auburn Sounds Graillon 3-64.dll"
            },
            "2": {
                "name": "TSE_808_2.0_x64",
                "display_name": "TSE808 失真效果器",
                "filter_kind": "vst_filter",
                "plugin_path": r"C:\Program Files\VSTPlugins\TSE_808_2.0_x64.dll"
            },
            "3": {
                "name": "TAL-Reverb-4-64",
                "display_name": "TAL 混响效果器",
                "filter_kind": "vst_filter",
                "plugin_path": r"C:\Program Files\VSTPlugins\TAL-Reverb-4-64.dll"
            }
        }
        
    def log(self, message):
        """打印日志"""
        timestamp = time.strftime("%H:%M:%S")
        print(f"[{timestamp}] {message}")
        
    def connect_obs(self, host="localhost", port=4455):
        """连接到OBS"""
        if not WEBSOCKET_AVAILABLE:
            self.log("❌ websocket-client库未安装")
            return False
            
        try:
            self.log("🔄 正在连接到OBS...")
            ws_url = f"ws://{host}:{port}"
            self.ws = websocket.create_connection(ws_url, timeout=5)
            
            # 接收Hello消息
            hello_raw = self.ws.recv()
            hello_data = json.loads(hello_raw)
            self.log(f"✅ 连接成功: OBS WebSocket v{hello_data.get('d', {}).get('obsWebSocketVersion', 'Unknown')}")
            
            if hello_data.get("op") != 0:
                raise ValueError("收到的第一个消息不是Hello")
                
            rpc_version = hello_data.get("d", {}).get("rpcVersion", 1)
            authentication_required = hello_data.get("d", {}).get("authentication") is not None
            
            if authentication_required:
                raise ConnectionAbortedError("OBS需要身份验证，请在OBS中禁用身份验证")
            
            # 发送Identify消息
            identify_payload = {
                "op": 1,
                "d": {
                    "rpcVersion": rpc_version,
                    "eventSubscriptions": 0
                }
            }
            self.ws.send(json.dumps(identify_payload))
            
            # 接收Identified消息
            identified_raw = self.ws.recv()
            identified_data = json.loads(identified_raw)
            
            if identified_data.get("op") == 2:
                self.is_connected = True
                self.log("🎉 OBS连接建立成功！")
                return True
            else:
                self.log("❌ 连接失败")
                return False
                
        except Exception as e:
            self.log(f"❌ 连接失败: {e}")
            return False
            
    def disconnect(self):
        """断开连接"""
        if self.ws:
            try:
                self.ws.close()
            except:
                pass
            self.ws = None
        self.is_connected = False
        self.log("👋 已断开OBS连接")
        
    def send_request(self, request_type, request_data=None, timeout=10):
        """发送请求并获取响应"""
        if not self.is_connected or not self.ws:
            return None
            
        self.request_id_counter += 1
        request_id = f"req-{int(time.time())}-{self.request_id_counter}"
        
        payload = {
            "op": 6,
            "d": {
                "requestType": request_type,
                "requestId": request_id,
                "requestData": request_data or {}
            }
        }
        
        try:
            self.ws.send(json.dumps(payload))
            
            start_time = time.time()
            while time.time() - start_time < timeout:
                try:
                    response_raw = self.ws.recv()
                    response_data = json.loads(response_raw)
                    
                    if (response_data.get("op") == 7 and 
                        response_data.get("d", {}).get("requestId") == request_id):
                        return response_data.get("d", {})
                        
                except websocket.WebSocketTimeoutException:
                    continue
                except:
                    continue
                    
            return None
            
        except Exception as e:
            self.log(f"❌ 发送请求失败: {e}")
            return None
            
    def get_media_sources(self):
        """获取媒体源列表"""
        self.log("🔍 正在获取媒体源列表...")
        
        response_data = self.send_request("GetInputList")
        if not response_data:
            self.log("❌ 获取媒体源失败")
            return []
            
        request_status = response_data.get("requestStatus", {})
        if not request_status.get("result"):
            error_msg = request_status.get("comment", "未知错误")
            self.log(f"❌ 获取媒体源失败: {error_msg}")
            return []
            
        inputs_data = response_data.get("responseData", {})
        inputs_list = inputs_data.get("inputs", [])
        
        sources = []
        self.log(f"✅ 成功获取到 {len(inputs_list)} 个输入源")
        
        for i, input_item in enumerate(inputs_list, 1):
            input_name = input_item.get('inputName', '')
            input_kind = input_item.get('inputKind', '')
            
            sources.append({
                'index': i,
                'name': input_name,
                'kind': input_kind
            })
            
            # 分类显示
            if input_kind in ['ffmpeg_source', 'vlc_source']:
                self.log(f"  {i}. 🎬 媒体源: {input_name} ({input_kind})")
            elif input_kind in ['wasapi_input_capture', 'wasapi_output_capture', 'pulse_input_capture']:
                self.log(f"  {i}. 🎵 音频源: {input_name} ({input_kind})")
            elif input_kind in ['dshow_input', 'v4l2_input']:
                self.log(f"  {i}. 📹 视频源: {input_name} ({input_kind})")
            else:
                self.log(f"  {i}. ⚪ 其他源: {input_name} ({input_kind})")
                
        return sources
        
    def get_filters(self, source_name):
        """获取指定媒体源的滤镜"""
        self.log(f"🔍 正在获取媒体源 '{source_name}' 的滤镜...")
        
        response_data = self.send_request("GetSourceFilterList", {"sourceName": source_name})
        if not response_data:
            self.log("❌ 获取滤镜失败")
            return []
            
        request_status = response_data.get("requestStatus", {})
        if not request_status.get("result"):
            error_msg = request_status.get("comment", "未知错误")
            self.log(f"❌ 获取滤镜失败: {error_msg}")
            return []
            
        filters_data = response_data.get("responseData", {})
        filters_list = filters_data.get("filters", [])
        
        filters = []
        vst_count = 0
        
        self.log(f"✅ 找到 {len(filters_list)} 个滤镜")
        
        for i, filter_item in enumerate(filters_list, 1):
            filter_name = filter_item.get('filterName', '')
            filter_kind = filter_item.get('filterKind', '')
            filter_enabled = filter_item.get('filterEnabled', False)
            
            filters.append({
                'index': i,
                'name': filter_name,
                'kind': filter_kind,
                'enabled': filter_enabled
            })
            
            # 检查是否为VST滤镜
            is_vst = 'vst' in filter_kind.lower()
            if is_vst:
                vst_count += 1
                
            # 显示滤镜信息
            enabled_text = "✅ 启用" if filter_enabled else "❌ 禁用"
            if is_vst:
                self.log(f"  {i}. 🎛️ VST滤镜: {filter_name} ({filter_kind}) {enabled_text}")
            else:
                self.log(f"  {i}. ⚪ 其他滤镜: {filter_name} ({filter_kind}) {enabled_text}")
                
        if vst_count > 0:
            self.log(f"🎛️ 共找到 {vst_count} 个VST滤镜")
        else:
            self.log("📭 未找到VST滤镜")
            
        return filters
        
    def add_vst_filter(self, source_name, filter_name, plugin_key):
        """添加VST滤镜"""
        if plugin_key not in self.vst_plugins:
            self.log(f"❌ 无效的插件选择: {plugin_key}")
            return False
            
        plugin_config = self.vst_plugins[plugin_key]
        plugin_path = plugin_config["plugin_path"]
        
        # 检查插件文件是否存在
        if not os.path.exists(plugin_path):
            self.log(f"❌ VST插件文件不存在: {plugin_path}")
            return False
            
        self.log(f"🔧 正在添加VST滤镜: {filter_name} ({plugin_config['display_name']})")
        
        try:
            # 检查滤镜是否已存在
            existing_response = self.send_request("GetSourceFilter", {
                "sourceName": source_name,
                "filterName": filter_name
            })
            
            if existing_response and existing_response.get("requestStatus", {}).get("result"):
                self.log(f"⚠️ 滤镜 '{filter_name}' 已存在，将先删除")
                
                delete_response = self.send_request("RemoveSourceFilter", {
                    "sourceName": source_name,
                    "filterName": filter_name
                })
                time.sleep(0.5)
            
            # 创建VST滤镜
            filter_settings = {
                "plugin_path": plugin_path
            }
            
            response = self.send_request("CreateSourceFilter", {
                "sourceName": source_name,
                "filterName": filter_name,
                "filterKind": plugin_config["filter_kind"],
                "filterSettings": filter_settings
            })
            
            if response and response.get("requestStatus", {}).get("result"):
                self.log(f"✅ VST滤镜 '{filter_name}' 添加成功")
                return True
            else:
                error_msg = response.get("requestStatus", {}).get("comment", "未知错误") if response else "无响应"
                self.log(f"❌ 创建VST滤镜失败: {error_msg}")
                return False
                
        except Exception as e:
            self.log(f"❌ 添加VST滤镜失败: {e}")
            return False
            
    def show_vst_plugins(self):
        """显示可用的VST插件"""
        self.log("📋 可用的VST插件:")
        for key, plugin in self.vst_plugins.items():
            exists = "✅" if os.path.exists(plugin["plugin_path"]) else "❌"
            self.log(f"  {key}. {exists} {plugin['display_name']}")
            self.log(f"     路径: {plugin['plugin_path']}")
            
    def interactive_mode(self):
        """交互模式"""
        self.log("🎛️ OBS VST滤镜管理器 - 命令行版本")
        
        if not WEBSOCKET_AVAILABLE:
            self.log("❌ 请先安装websocket-client: pip install websocket-client")
            return
            
        # 连接OBS
        if not self.connect_obs():
            return
            
        try:
            while True:
                print("\n" + "="*50)
                print("请选择操作:")
                print("1. 获取媒体源列表")
                print("2. 获取滤镜列表")
                print("3. 添加VST滤镜")
                print("4. 显示VST插件")
                print("0. 退出")
                print("="*50)
                
                choice = input("请输入选择 (0-4): ").strip()
                
                if choice == "0":
                    break
                elif choice == "1":
                    self.get_media_sources()
                elif choice == "2":
                    source_name = input("请输入媒体源名称: ").strip()
                    if source_name:
                        self.get_filters(source_name)
                elif choice == "3":
                    self.show_vst_plugins()
                    source_name = input("请输入媒体源名称: ").strip()
                    if not source_name:
                        continue
                    plugin_key = input("请选择VST插件 (1-3): ").strip()
                    if plugin_key not in self.vst_plugins:
                        self.log("❌ 无效的插件选择")
                        continue
                    filter_name = input("请输入滤镜名称: ").strip()
                    if not filter_name:
                        continue
                    self.add_vst_filter(source_name, filter_name, plugin_key)
                elif choice == "4":
                    self.show_vst_plugins()
                else:
                    self.log("❌ 无效的选择")
                    
        except KeyboardInterrupt:
            self.log("\n👋 用户中断")
        finally:
            self.disconnect()

def main():
    """主函数"""
    manager = SimpleOBSVST()
    manager.interactive_mode()

if __name__ == "__main__":
    main()
