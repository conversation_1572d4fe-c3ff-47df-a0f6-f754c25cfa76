# 🚀 OBS VST滤镜管理器 - 快速使用指南

## 🎉 恭喜！程序已成功运行

根据你的截图，程序已经成功连接到OBS并显示了媒体源。现在可以开始使用VST滤镜管理功能了！

## 📁 你的VST插件路径

程序已配置为使用你的实际插件路径：
```
C:\Program Files\VSTPlugins\
├── Auburn Sounds Graillon 3-64.dll  ✅ 音调变声器
├── TAL-Reverb-4-64.dll              ✅ 混响效果器
└── TSE_808_2.0_x64.dll              ✅ 失真效果器
```

## 🎯 快速操作步骤

### 第1步：选择媒体源
- 在"选择媒体源"下拉框中选择你要添加VST滤镜的音频源
- 通常选择麦克风或音频输入设备

### 第2步：查看现有滤镜
- 选择媒体源后，下方的滤镜列表会自动显示该源的所有滤镜
- VST滤镜会用🎛️图标特别标记

### 第3步：添加VST滤镜
1. 在"添加VST滤镜"区域选择要添加的插件：
   - **Auburn Sounds Graillon 3-64** - 用于音调变化
   - **TSE_808_2.0_x64** - 用于失真效果
   - **TAL-Reverb-4-64** - 用于混响效果

2. 在"滤镜名称"框中输入自定义名称，例如：
   - "我的变声器"
   - "音调调节"
   - "失真效果"

3. 点击"添加VST滤镜"按钮

### 第4步：验证添加结果
- 查看操作日志确认添加是否成功
- 滤镜列表会自动刷新显示新添加的VST滤镜
- 在OBS中也可以看到新添加的滤镜

## 🎛️ VST插件功能说明

### Auburn Sounds Graillon 3-64 (音调变声器)
- **用途**：改变声音的音调，实现变声效果
- **适用场景**：
  - 直播时的趣味变声
  - 录制时的音调调节
  - 创建不同的角色声音

### TSE_808_2.0_x64 (失真效果器)
- **用途**：添加温暖的失真和过载效果
- **适用场景**：
  - 音乐制作中的失真效果
  - 人声增色处理
  - 创建复古音效

### TAL-Reverb-4-64 (混响效果器)
- **用途**：添加空间混响，增加声音的深度感
- **适用场景**：
  - 模拟不同房间的声学效果
  - 增加声音的空间感
  - 专业音频后期处理

## 💡 使用技巧

### 1. 滤镜命名建议
- 使用描述性的名称，如"Graillon变声器"、"808失真"
- 避免使用特殊字符
- 名称不能与现有滤镜重复

### 2. 滤镜顺序
在OBS中，滤镜的处理顺序很重要：
```
音频输入 → 音调调节(Graillon) → 失真效果(TSE808) → 混响(TAL) → 输出
```

### 3. 参数调节
- 添加VST滤镜后，可以在OBS中双击滤镜打开参数界面
- 每个插件都有自己的参数设置界面
- 建议先使用默认参数，然后根据需要微调

## ⚠️ 注意事项

### 滤镜重复处理
- 如果添加同名滤镜，程序会自动删除旧的再创建新的
- 这是正常行为，确保滤镜配置的一致性

### 插件加载时间
- VST插件添加后需要一些时间加载
- 如果立即在OBS中看不到效果，请等待几秒钟

### 音频质量
- 过多的音频处理可能影响音质
- 建议根据实际需要选择合适的插件组合

## 🔧 故障排除

### 问题1：插件添加失败
**可能原因**：
- 插件文件不存在
- 路径配置错误
- 权限不足

**解决方法**：
1. 确认插件文件存在于 `C:\Program Files\VSTPlugins\` 目录
2. 尝试以管理员身份运行程序
3. 检查操作日志中的具体错误信息

### 问题2：滤镜不生效
**可能原因**：
- 插件未完全加载
- OBS中滤镜被禁用
- 参数设置问题

**解决方法**：
1. 在OBS中检查滤镜是否启用
2. 双击滤镜打开参数界面确认设置
3. 重启OBS重新加载插件

### 问题3：音频异常
**可能原因**：
- 多个音频处理插件冲突
- 参数设置过于极端

**解决方法**：
1. 逐个禁用滤镜找出问题源
2. 重置插件参数到默认值
3. 调整滤镜的处理顺序

## 🎊 开始享受VST滤镜功能

现在你已经掌握了基本使用方法，可以：

1. **实验不同的插件组合**
2. **调整参数创建独特的音效**
3. **为不同场景保存不同的配置**
4. **在直播或录制中使用这些效果**

祝你使用愉快！如果有任何问题，可以查看详细的操作日志获取更多信息。
