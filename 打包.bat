@echo off
chcp 65001 >nul
echo.
echo ==========================================
echo 🚀 OBS去重工具打包脚本
echo ==========================================
echo.

:: 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python未安装或未添加到PATH
    echo 请先安装Python并添加到系统PATH
    pause
    exit /b 1
)

:: 检查必要文件
echo 📋 检查必要文件...
if not exist "main.py" (
    echo ❌ 找不到 main.py
    pause
    exit /b 1
)
if not exist "main_module.py" (
    echo ❌ 找不到 main_module.py
    pause
    exit /b 1
)
if not exist "obs2.ico" (
    echo ❌ 找不到 obs2.ico
    pause
    exit /b 1
)
if not exist "zuozhe.png" (
    echo ❌ 找不到 zuozhe.png
    pause
    exit /b 1
)

echo ✅ 必要文件检查完成

:: 询问打包方式
echo.
echo 请选择打包方式:
echo 1. 快速打包 (推荐)
echo 2. 完整打包 (包含PYD转换选项)
echo 3. 仅转换PYD
echo.
set /p choice="请输入选择 (1-3): "

if "%choice%"=="1" (
    echo.
    echo 🔧 开始快速打包...
    python quick_build.py
) else if "%choice%"=="2" (
    echo.
    echo 🔧 开始完整打包...
    python build_script.py
) else if "%choice%"=="3" (
    echo.
    echo 🔧 开始转换PYD...
    python convert_to_pyd.py
) else (
    echo ❌ 无效选择
    pause
    exit /b 1
)

echo.
echo ==========================================
echo 🎉 打包流程完成
echo ==========================================
pause
