# 🧬 VST Chunk数据操作说明

## 🎯 重要发现

你的观察非常准确！**OBS确实没有权限直接控制VST插件内部的参数**。

### 📋 VST插件在OBS中的工作原理：

1. **VST插件独立运行** - 插件有自己的界面和参数系统
2. **OBS只能保存/加载状态** - 通过chunk数据（二进制格式）
3. **参数调节必须在插件内部** - 或者通过修改chunk数据间接实现
4. **chunk数据包含完整状态** - 所有参数、设置、预设等

## 🆕 新的解决方案

基于你的发现，我已经重新设计了参数控制方法：

### 🧬 Chunk数据操作方式
- ✅ **读取chunk数据** - 获取插件的完整二进制状态
- ✅ **分析参数位置** - 找出参数在chunk中的具体位置
- ✅ **修改二进制数据** - 直接修改chunk中的参数值
- ✅ **重新加载状态** - 让OBS加载修改后的chunk数据

## 🔧 新增功能

### 1. Chunk数据参数设置
```python
def set_filter_parameter_via_chunk(source_name, filter_name, param_name, value):
    # 获取当前chunk数据
    # 解码为二进制数据
    # 根据参数位置修改数值
    # 重新编码并设置回OBS
```

### 2. 参数位置映射
```python
# Graillon插件的参数位置（示例）
graillon_positions = {
    'pitch': 16,      # 音调参数在第16字节位置
    'formant': 24,    # 共振峰在第24字节位置
    'mix': 32,        # 混合比例在第32字节位置
}
```

### 3. 智能数值转换
```python
# 音调参数：-12到+12半音 -> 0到1范围
normalized_pitch = (pitch_value + 12) / 24

# 百分比参数：0到100% -> 0到1范围  
normalized_percent = percent_value / 100.0
```

## 🚀 使用新的调试功能

### 调试窗口新增按钮：
1. **"分析Chunk位置"** - 分析chunk数据中的浮点数位置
2. **"测试Chunk修改"** - 测试通过chunk数据修改参数

### 使用步骤：
1. **选择VST滤镜** - 确保插件已完全加载
2. **打开调试窗口** - 点击"调试参数"
3. **分析chunk位置** - 点击"分析Chunk位置"查看可能的参数位置
4. **测试修改** - 输入参数名和值，点击"测试Chunk修改"
5. **验证效果** - 在OBS中听音频效果是否改变

## 📊 Chunk数据分析

### 典型的chunk数据结构：
```
位置 0-15:   插件头部信息
位置 16-19:  第一个参数（4字节浮点数）
位置 20-23:  第二个参数（4字节浮点数）
位置 24-27:  第三个参数（4字节浮点数）
...
```

### 参数值范围：
- **大多数VST参数使用0.0-1.0范围**
- **音调参数可能使用-1.0到+1.0**
- **某些参数可能使用实际数值范围**

## 🔍 找出正确的参数位置

### 方法1：对比分析
```
1. 获取默认状态的chunk数据
2. 在VST插件界面中调节一个参数
3. 重新获取chunk数据
4. 对比两个chunk数据，找出变化的位置
5. 该位置就是这个参数的存储位置
```

### 方法2：模式识别
```
1. 分析chunk中的浮点数
2. 寻找0.0-1.0范围内的值
3. 这些位置很可能是参数存储位置
4. 通过测试确认具体对应关系
```

## 💡 实际操作示例

### 示例1：找出Graillon的pitch参数位置
```
1. 打开调试窗口
2. 点击"分析Chunk位置" - 发现位置16有值0.5
3. 在OBS中双击Graillon滤镜，打开插件界面
4. 调节pitch参数到+2半音
5. 回到调试窗口，再次分析chunk位置
6. 发现位置16的值变成了0.583（约等于(2+12)/24）
7. 确认位置16就是pitch参数位置
```

### 示例2：测试chunk修改
```
1. 在调试窗口输入：参数名="pitch", 参数值="5"
2. 点击"测试Chunk修改"
3. 程序会：
   - 计算normalized值：(5+12)/24 = 0.708
   - 修改chunk数据位置16为0.708
   - 重新加载chunk数据到OBS
4. 听音频效果，应该能听到+5半音的音调变化
```

## ⚠️ 重要注意事项

### 参数位置需要实际测试确定
- 我提供的位置映射只是估算
- 不同版本的插件可能有不同的chunk结构
- 需要通过实际测试找出正确位置

### 数值转换需要调整
- 不同参数的数值范围可能不同
- 需要通过测试找出正确的转换公式
- 某些参数可能使用非线性映射

### 操作风险
- 错误的chunk数据可能导致插件崩溃
- 建议先备份原始chunk数据
- 小幅度修改参数值进行测试

## 🎯 下一步计划

### 1. 确定参数位置
```
对于你的三个插件，我们需要：
1. 分析每个插件的chunk数据结构
2. 找出关键参数的确切位置
3. 确定正确的数值转换方法
```

### 2. 完善参数映射
```
建立准确的参数位置映射表：
- Graillon: pitch, formant, mix的确切位置
- TSE808: drive, tone, level的确切位置  
- TAL-Reverb: roomsize, damping, mix的确切位置
```

### 3. 优化用户体验
```
一旦确定了正确的参数位置：
- 快速调节功能将真正有效
- 参数预设可以正常工作
- 实时调节将立即生效
```

## 🎉 现在开始实际测试

现在你可以：

1. **选择一个VST滤镜**
2. **打开调试窗口**
3. **点击"分析Chunk位置"** - 查看可能的参数位置
4. **在OBS中手动调节插件参数** - 打开插件界面调节
5. **再次分析chunk位置** - 对比找出变化的位置
6. **测试chunk修改** - 使用找到的位置进行测试

通过这种方法，我们可以逐步找出每个插件的正确参数位置，然后快速调节功能就能真正工作了！

这是一个更底层但更有效的解决方案。让我们一起通过实际测试来完善这个系统吧！
