
from setuptools import setup
from Cython.Build import cythonize
import numpy

# 编译器指令
compiler_directives = {
    'language_level': 3,
    'boundscheck': False,
    'wraparound': False,
    'initializedcheck': False,
    'cdivision': True,
    'embedsignature': True,
}

# 要编译的文件
ext_modules = cythonize([
    "main_module.py",
], compiler_directives=compiler_directives)

setup(
    ext_modules=ext_modules,
    zip_safe=False,
)
