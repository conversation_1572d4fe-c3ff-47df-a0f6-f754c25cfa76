#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OBS去重工具打包脚本
包含转pyd和PyInstaller打包流程
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

def print_step(step_name):
    """打印步骤信息"""
    print(f"\n{'='*60}")
    print(f"🔧 {step_name}")
    print(f"{'='*60}")

def run_command(command, description):
    """运行命令并处理错误"""
    print(f"📝 {description}")
    print(f"💻 执行命令: {command}")
    
    try:
        result = subprocess.run(command, shell=True, check=True, 
                              capture_output=True, text=True, encoding='utf-8')
        print(f"✅ {description} 成功")
        if result.stdout:
            print(f"输出: {result.stdout}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} 失败")
        print(f"错误代码: {e.returncode}")
        if e.stdout:
            print(f"标准输出: {e.stdout}")
        if e.stderr:
            print(f"错误输出: {e.stderr}")
        return False
    except Exception as e:
        print(f"❌ {description} 出现异常: {e}")
        return False

def check_dependencies():
    """检查必要的依赖"""
    print_step("检查依赖")
    
    dependencies = [
        ("cython", "Cython"),
        ("pyinstaller", "PyInstaller"),
        ("PyQt5", "PyQt5")
    ]
    
    missing_deps = []
    
    for module, name in dependencies:
        try:
            __import__(module)
            print(f"✅ {name} 已安装")
        except ImportError:
            print(f"❌ {name} 未安装")
            missing_deps.append(name)
    
    if missing_deps:
        print(f"\n⚠️ 缺少依赖: {', '.join(missing_deps)}")
        print("请先安装缺少的依赖:")
        for dep in missing_deps:
            if dep == "Cython":
                print(f"  pip install Cython")
            elif dep == "PyInstaller":
                print(f"  pip install PyInstaller")
            elif dep == "PyQt5":
                print(f"  pip install PyQt5")
        return False
    
    return True

def convert_to_pyd():
    """将Python文件转换为pyd格式"""
    print_step("转换Python文件为PYD格式")
    
    # 需要转换的Python文件
    python_files = [
        "main_module.py",
        # 可以添加其他需要转换的文件
    ]
    
    # 创建setup_cython.py文件
    setup_content = '''
from setuptools import setup
from Cython.Build import cythonize
import sys

# 设置编译选项
compiler_directives = {
    'language_level': 3,
    'boundscheck': False,
    'wraparound': False,
    'initializedcheck': False,
    'cdivision': True,
}

setup(
    ext_modules=cythonize([
        "main_module.py",
    ], compiler_directives=compiler_directives),
    zip_safe=False,
)
'''
    
    with open("setup_cython.py", "w", encoding="utf-8") as f:
        f.write(setup_content)
    
    print("📝 已创建 setup_cython.py")
    
    # 执行Cython编译
    success = run_command(
        "python setup_cython.py build_ext --inplace",
        "编译Python文件为PYD"
    )
    
    if success:
        # 检查生成的pyd文件
        pyd_files = list(Path(".").glob("*.pyd"))
        if pyd_files:
            print(f"✅ 成功生成PYD文件:")
            for pyd_file in pyd_files:
                print(f"   - {pyd_file}")
        else:
            print("⚠️ 未找到生成的PYD文件")
    
    # 清理临时文件
    cleanup_cython_files()
    
    return success

def cleanup_cython_files():
    """清理Cython编译产生的临时文件"""
    print("🧹 清理Cython临时文件...")
    
    # 删除build目录
    if os.path.exists("build"):
        shutil.rmtree("build")
        print("   - 删除 build 目录")
    
    # 删除.c文件
    c_files = list(Path(".").glob("*.c"))
    for c_file in c_files:
        os.remove(c_file)
        print(f"   - 删除 {c_file}")
    
    # 删除setup_cython.py
    if os.path.exists("setup_cython.py"):
        os.remove("setup_cython.py")
        print("   - 删除 setup_cython.py")

def create_pyinstaller_spec():
    """创建PyInstaller规格文件"""
    print_step("创建PyInstaller规格文件")
    
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('obs2.ico', '.'),
        ('obs3.ico', '.'),
        ('zuozhe.png', '.'),
        ('platforms', 'platforms'),
        ('saved_colors', 'saved_colors'),
        ('*.lua', '.'),
        ('插件去重功能说明.md', '.'),
    ],
    hiddenimports=[
        'PyQt5.QtCore',
        'PyQt5.QtGui', 
        'PyQt5.QtWidgets',
        'websockets',
        'websocket',
        'asyncio',
        'json',
        'random',
        'time',
        'os',
        'sys',
        'traceback',
        'hmac',
        'hashlib',
        'base64',
        'webbrowser',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='OBS去重工具',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='obs2.ico',
    version_file=None,
)
'''
    
    with open("obs_tool.spec", "w", encoding="utf-8") as f:
        f.write(spec_content)
    
    print("✅ 已创建 obs_tool.spec")
    return True

def build_executable():
    """使用PyInstaller构建可执行文件"""
    print_step("构建可执行文件")
    
    # 使用spec文件构建
    success = run_command(
        "pyinstaller obs_tool.spec --clean --noconfirm",
        "使用PyInstaller构建可执行文件"
    )
    
    if success:
        # 检查生成的exe文件
        exe_path = Path("dist/OBS去重工具.exe")
        if exe_path.exists():
            file_size = exe_path.stat().st_size / (1024 * 1024)  # MB
            print(f"✅ 成功生成可执行文件:")
            print(f"   - 路径: {exe_path}")
            print(f"   - 大小: {file_size:.1f} MB")
        else:
            print("❌ 未找到生成的可执行文件")
            return False
    
    return success

def cleanup_build_files():
    """清理构建过程中的临时文件"""
    print_step("清理构建文件")
    
    cleanup_items = [
        "build",
        "__pycache__",
        "obs_tool.spec",
        "*.pyd",
    ]
    
    for item in cleanup_items:
        if "*" in item:
            # 处理通配符
            files = list(Path(".").glob(item))
            for file in files:
                if file.is_file():
                    os.remove(file)
                    print(f"   - 删除文件: {file}")
        else:
            if os.path.exists(item):
                if os.path.isdir(item):
                    shutil.rmtree(item)
                    print(f"   - 删除目录: {item}")
                else:
                    os.remove(item)
                    print(f"   - 删除文件: {item}")

def main():
    """主函数"""
    print("🚀 OBS去重工具打包脚本")
    print("=" * 60)
    
    try:
        # 1. 检查依赖
        if not check_dependencies():
            print("❌ 依赖检查失败，请安装缺少的依赖后重试")
            return False
        
        # 2. 转换为pyd（可选，如果需要代码保护）
        convert_pyd = input("\n是否转换Python文件为PYD格式？(y/N): ").lower().strip()
        if convert_pyd == 'y':
            if not convert_to_pyd():
                print("❌ PYD转换失败")
                return False
        
        # 3. 创建PyInstaller规格文件
        if not create_pyinstaller_spec():
            print("❌ 创建规格文件失败")
            return False
        
        # 4. 构建可执行文件
        if not build_executable():
            print("❌ 构建可执行文件失败")
            return False
        
        # 5. 清理临时文件
        cleanup_build_files()
        
        print_step("打包完成")
        print("🎉 打包成功！")
        print("📁 可执行文件位置: dist/OBS去重工具.exe")
        print("💡 提示: 可执行文件已包含图标，支持任务栏和程序左上角显示")
        
        return True
        
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断了打包过程")
        return False
    except Exception as e:
        print(f"\n❌ 打包过程中出现异常: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    input("\n按回车键退出...")
    sys.exit(0 if success else 1)
