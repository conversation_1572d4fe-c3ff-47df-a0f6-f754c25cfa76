#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试PYD转换问题
"""

import subprocess
import sys
import os

def run_command_debug(cmd):
    """运行命令并显示详细输出"""
    print(f"🔧 执行命令: {cmd}")
    print("-" * 50)
    
    try:
        # 不捕获输出，直接显示到控制台
        result = subprocess.run(cmd, shell=True, text=True, encoding='utf-8')
        print("-" * 50)
        print(f"命令执行完成，返回码: {result.returncode}")
        return result.returncode == 0
    except Exception as e:
        print(f"执行异常: {e}")
        return False

def main():
    """主函数"""
    print("🐛 PYD转换调试工具")
    print("=" * 50)
    
    # 1. 检查Cython
    print("1️⃣ 检查Cython...")
    if not run_command_debug("python -c \"import Cython; print(f'Cython版本: {Cython.__version__}')\""):
        print("❌ Cython检查失败")
        return
    
    # 2. 检查文件
    print("\n2️⃣ 检查文件...")
    if not os.path.exists("main_module.py"):
        print("❌ main_module.py不存在")
        return
    else:
        print("✅ main_module.py存在")
    
    # 3. 创建简单的setup文件
    print("\n3️⃣ 创建setup文件...")
    setup_content = '''# -*- coding: utf-8 -*-
from setuptools import setup
from Cython.Build import cythonize

setup(
    ext_modules=cythonize("main_module.py", language_level=3),
    zip_safe=False,
)
'''
    
    with open("debug_setup.py", "w", encoding="utf-8") as f:
        f.write(setup_content)
    print("✅ 创建了debug_setup.py")
    
    # 4. 运行编译
    print("\n4️⃣ 运行编译...")
    success = run_command_debug("python debug_setup.py build_ext --inplace")
    
    if success:
        # 5. 检查结果
        print("\n5️⃣ 检查结果...")
        pyd_files = []
        for file in os.listdir("."):
            if file.endswith(".pyd"):
                pyd_files.append(file)
        
        if pyd_files:
            print("🎉 PYD文件生成成功:")
            for pyd in pyd_files:
                size = os.path.getsize(pyd) / 1024
                print(f"   - {pyd} ({size:.1f} KB)")
        else:
            print("⚠️ 未找到PYD文件")
    else:
        print("❌ 编译失败")
    
    # 清理
    if os.path.exists("debug_setup.py"):
        os.remove("debug_setup.py")
    
    print("\n" + "=" * 50)
    print("调试完成")

if __name__ == "__main__":
    main()
    input("按回车键退出...")
