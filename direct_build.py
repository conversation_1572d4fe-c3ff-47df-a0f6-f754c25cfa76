#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接打包脚本（不转PYD）
"""

import os
import sys
import subprocess
import shutil

def main():
    """主函数"""
    print("🚀 直接打包OBS去重工具")
    print("=" * 50)
    
    # 清理旧文件
    print("🧹 清理旧文件...")
    for item in ["build", "dist"]:
        if os.path.exists(item):
            shutil.rmtree(item)
            print(f"   删除: {item}")
    
    # 构建命令
    cmd = [
        "pyinstaller",
        "--onefile",
        "--windowed", 
        "--icon=obs2.ico",
        "--name=OBS去重工具",
        "--clean",
        "--noconfirm",
        
        # 添加数据文件
        "--add-data", "obs2.ico;.",
        "--add-data", "obs3.ico;.",
        "--add-data", "zuozhe.png;.",
        "--add-data", "platforms;platforms",
        "--add-data", "saved_colors;saved_colors",
        "--add-data", "压缩.lua;.",
        "--add-data", "增益.lua;.",
        "--add-data", "随机音频播放大小.lua;.",
        "--add-data", "插件去重功能说明.md;.",
        
        # 隐藏导入
        "--hidden-import", "PyQt5.QtCore",
        "--hidden-import", "PyQt5.QtGui", 
        "--hidden-import", "PyQt5.QtWidgets",
        "--hidden-import", "websockets",
        "--hidden-import", "websocket",
        "--hidden-import", "asyncio",
        "--hidden-import", "webbrowser",
        
        "main.py"
    ]
    
    print("🔨 开始打包...")
    print(f"💻 命令: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, check=True)
        
        # 检查结果
        exe_path = "dist/OBS去重工具.exe"
        if os.path.exists(exe_path):
            size = os.path.getsize(exe_path) / (1024 * 1024)
            print(f"\n🎉 打包成功！")
            print(f"📁 文件: {exe_path}")
            print(f"📏 大小: {size:.1f} MB")
            print(f"🖼️ 图标: 已包含")
            print(f"🖼️ 资源: 已包含zuozhe.png")
            return True
        else:
            print("❌ 未找到生成的exe文件")
            return False
            
    except subprocess.CalledProcessError as e:
        print(f"❌ 打包失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 异常: {e}")
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n✨ 打包完成！")
    else:
        print("\n💥 打包失败")
    
    input("按回车键退出...")
