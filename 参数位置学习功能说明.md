# 🎓 VST参数位置学习功能说明

## 🎯 解决参数位置未知的问题

我看到你遇到了"未知参数位置"的错误。这是因为程序不知道你的VST插件中参数在chunk数据中的确切位置。

### 🆕 新增学习功能

我已经添加了**"学习参数位置"**功能，可以通过对比修改前后的chunk数据，自动找出参数的确切位置！

## 🚀 使用学习功能的步骤

### 第1步：准备学习
1. **选择VST滤镜** - 在主界面选中要学习的VST滤镜
2. **打开调试窗口** - 点击"调试参数"按钮
3. **输入参数名** - 在"参数名"框中输入要学习的参数，如"pitch"

### 第2步：开始学习
1. **点击"学习参数位置"** - 会弹出学习窗口
2. **点击"1. 获取当前状态"** - 记录当前的chunk数据
3. **在OBS中调节参数**：
   - 双击VST滤镜，打开插件界面
   - **只调节你要学习的那个参数**（比如pitch）
   - 调节到一个明显不同的值（比如从0调到+5半音）
   - **不要调节其他参数**
4. **点击"2. 获取修改后状态"** - 记录修改后的chunk数据

### 第3步：分析结果
程序会自动：
1. **对比两个chunk数据** - 找出所有发生变化的位置
2. **显示变化详情** - 在日志中显示每个位置的变化
3. **推荐最佳位置** - 选择变化最大的位置作为参数位置
4. **询问是否保存** - 确认后保存参数位置映射

## 📊 学习示例

### 示例1：学习Graillon的pitch参数
```
1. 输入参数名: "pitch"
2. 点击"学习参数位置"
3. 点击"获取当前状态" - 记录默认状态
4. 在OBS中打开Graillon插件界面
5. 将pitch从0调节到+5半音
6. 点击"获取修改后状态"
7. 程序分析结果：
   位置 16: 0.500000 → 0.708333 (变化: +0.208333)
   位置 24: 1.000000 → 1.000000 (变化: +0.000000)
   推荐位置: 16 (变化最大)
8. 确认保存位置16为pitch参数位置
```

### 示例2：学习TSE808的drive参数
```
1. 输入参数名: "drive"
2. 开始学习流程
3. 在插件中将drive从0%调到50%
4. 程序发现位置20有变化: 0.0 → 0.5
5. 保存位置20为drive参数位置
```

## 🔧 改进的错误处理

### 智能参数位置猜测
如果参数位置未知，程序现在会：
1. **显示详细错误信息** - 告诉你哪些参数位置是已知的
2. **尝试智能猜测** - 基于chunk数据分析猜测可能位置
3. **提供学习建议** - 建议使用学习功能找出正确位置

### 更好的调试信息
```
⚠️ 参数 'pitch' 不在已知位置列表中
📋 已知参数: ['param1', 'param2', 'param3']
🤔 尝试猜测位置: 16
📊 找到 5 个可能的参数位置:
  位置 16: 0.500000
  位置 24: 1.000000
  位置 32: 0.750000
```

## 💡 学习技巧

### 1. 一次只学习一个参数
- **重要**：每次学习只调节一个参数
- 如果同时调节多个参数，无法确定哪个位置对应哪个参数

### 2. 调节幅度要明显
- 调节幅度要足够大，确保chunk数据有明显变化
- 比如音调参数调节±5半音，百分比参数调节50%

### 3. 记录学习结果
- 学习成功后记录下参数位置
- 可以手动更新程序中的参数位置映射

### 4. 验证学习结果
- 学习完成后，使用"测试Chunk修改"验证位置是否正确
- 如果测试失败，重新学习或尝试其他位置

## 🎯 建议的学习顺序

### 对于Graillon插件：
1. **先学习pitch参数** - 最重要的音调参数
2. **再学习mix参数** - 干湿混合比例
3. **最后学习formant参数** - 共振峰调节

### 对于TSE808插件：
1. **先学习drive参数** - 失真强度
2. **再学习level参数** - 输出电平
3. **最后学习tone参数** - 音色控制

### 对于TAL-Reverb插件：
1. **先学习mix参数** - 混响混合
2. **再学习roomsize参数** - 房间大小
3. **最后学习damping参数** - 阻尼控制

## ⚠️ 注意事项

### 学习过程中：
- **保持OBS音频播放** - 这样可以听到参数变化的效果
- **不要关闭插件界面** - 学习过程中保持插件界面打开
- **确认参数变化** - 调节参数时确认界面上的数值确实改变了

### 学习结果：
- **验证准确性** - 学习完成后测试参数设置是否有效
- **记录位置信息** - 将成功的位置记录下来供以后使用
- **分享经验** - 成功的位置映射可以分享给其他用户

## 🎉 开始学习

现在你可以：

1. **选择有问题的VST滤镜**
2. **打开调试窗口**
3. **输入要学习的参数名**（如"pitch"）
4. **点击"学习参数位置"**
5. **按照学习窗口的指导完成学习**
6. **测试学习结果是否正确**

通过这个学习功能，我们可以逐步建立起每个VST插件的完整参数位置映射表，然后快速调节功能就能真正工作了！

试试先学习一个简单的参数，比如Graillon的pitch参数吧！🎓✨
