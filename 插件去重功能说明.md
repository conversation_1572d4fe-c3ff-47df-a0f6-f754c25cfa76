# 🎛️ 插件去重功能使用说明

## 🎯 功能概述

插件去重功能是音频去重系统中的一个新模块，它可以独立管理3个VST2x插件，每个插件都有自己的触发间隔和持续时间设置，通过随机启用不同的音频效果来增加音频变化，避免重复检测。

## 🔧 功能特点

### 支持的插件
系统预配置了以下3个VST插件：

1. **CamelCrusher 失真效果**
   - 插件名称: `camelCrusher`
   - 滤镜名称: `CamelCrusher_去重`
   - 插件路径: `C:\Program Files\VSTPlugins\camelCrusher.dll`

2. **TAL 混响效果**
   - 插件名称: `TAL-Reverb-4-64`
   - 滤镜名称: `TAL_Reverb_去重`
   - 插件路径: `C:\Program Files\VSTPlugins\TAL-Reverb-4-64.dll`

3. **TSE808 失真效果**
   - 插件名称: `TSE_808_2.0_x64`
   - 滤镜名称: `TSE808_去重`
   - 插件路径: `C:\Program Files\VSTPlugins\TSE_808_2.0_x64.dll`

### 工作原理

1. **独立激活**: 每个插件都有独立的定时器，可以同时运行
2. **个性化间隔**: 每个插件都有自己的触发间隔范围设置
3. **个性化持续时间**: 每个插件都有自己的启用持续时间设置
4. **自动管理**: 每个插件按照自己的时间设置独立运行，互不干扰
5. **智能加载与重新加载**:
   - 启用时会检查插件文件是否存在
   - 如果插件不存在会自动尝试重新加载
   - 激活插件失败时会自动重新加载并重试
   - 重新加载过程：删除旧滤镜 → 重新创建新滤镜 → 重新激活

## 🎮 使用方法

### 1. 基本设置

1. **连接OBS**: 确保已连接到OBS Studio
2. **选择音频源**: 在"音频媒体源"下拉框中选择要应用效果的音频源
3. **打开插件去重**: 在音频去重功能标签页中，点击"🎛️ 插件去重"子标签

### 2. 参数配置

每个插件都有独立的参数设置区域：

#### CamelCrusher 失真效果
- **触发间隔**: 默认10-30秒，可调整范围1-300秒
- **持续时间**: 默认5-15秒，可调整范围1-120秒

#### TAL 混响效果
- **触发间隔**: 默认15-40秒，可调整范围1-300秒
- **持续时间**: 默认8-20秒，可调整范围1-120秒

#### TSE808 失真效果
- **触发间隔**: 默认12-35秒，可调整范围1-300秒
- **持续时间**: 默认6-18秒，可调整范围1-120秒

**说明**: 每个插件会在自己的间隔范围内随机选择触发时间，在持续时间范围内随机选择启用时长

### 3. 启用功能

1. **下载插件**: 如果插件未安装，点击"📥 下载插件"按钮下载
2. **勾选启用**: 勾选"启用插件去重"复选框
3. **自动创建**: 系统会自动创建所有3个VST插件滤镜
4. **开始运行**: 功能启动后每个插件会按照自己的时间设置独立运行

## 📊 工作流程

```
启动功能 → 创建所有插件滤镜 → 启动所有插件的独立定时器

插件1: 等待间隔时间1 → 激活 → 持续时间1后禁用 → 等待间隔时间1 → 循环...
插件2: 等待间隔时间2 → 激活 → 持续时间2后禁用 → 等待间隔时间2 → 循环...
插件3: 等待间隔时间3 → 激活 → 持续时间3后禁用 → 等待间隔时间3 → 循环...

停止功能 → 停止所有定时器 → 禁用所有插件
```

## ⚙️ 技术实现

### 核心组件

1. **控制结构**: `plugin_dedup_control` 字典管理所有状态
2. **定时器**: 
   - `timer`: 控制插件激活间隔
   - `duration_timer`: 控制插件持续时间
3. **插件管理**: 自动创建、启用、禁用VST滤镜

### 主要函数

- `toggle_plugin_dedup_control()`: 启用/禁用插件去重
- `create_all_plugin_filters()`: 创建所有插件滤镜
- `start_all_plugin_timers()`: 启动所有插件的独立定时器
- `stop_all_plugin_timers()`: 停止所有插件的定时器
- `schedule_plugin_activation()`: 安排指定插件的激活
- `activate_plugin_by_index()`: 激活指定索引的插件
- `deactivate_plugin_by_index()`: 禁用指定索引的插件
- `deactivate_all_plugins()`: 禁用所有插件
- `update_plugin_setting()`: 更新指定插件的设置
- `set_filter_enabled()`: 启用/禁用滤镜

## 🔍 注意事项

### 插件下载与安装
- 每个插件都有对应的"📥 下载插件"按钮
- 点击下载按钮会自动打开浏览器到下载页面
- 下载完成后将插件文件放置到：`C:\Program Files\VSTPlugins\`
- 系统会自动检测插件文件，已安装的插件会显示"✅ 已安装"状态
- 可以点击"🔄 刷新状态"按钮手动刷新插件状态

### 使用建议
- **间隔时间**: 每个插件可以设置不同的间隔时间，避免同时触发
- **持续时间**: 根据插件效果特点设置合适的持续时间
- **音频源**: 确保选择正确的音频媒体源
- **插件兼容**: 确保VST插件与OBS版本兼容
- **个性化设置**: 可以根据每个插件的特点设置不同的时间参数

### 📥 插件下载功能

系统内置了便捷的插件下载功能：

#### 下载按钮
- 每个插件都有独立的"📥 下载插件"按钮
- 只有在插件未安装时才显示下载按钮
- 已安装的插件会显示"✅ 已安装"状态

#### 下载流程
1. **点击下载**: 点击对应插件的"📥 下载插件"按钮
2. **打开浏览器**: 系统会自动打开默认浏览器到下载页面
3. **下载文件**: 在浏览器中完成插件文件下载
4. **安装插件**: 将下载的插件文件放置到 `C:\Program Files\VSTPlugins\`
5. **刷新状态**: 点击"🔄 刷新状态"按钮或重新启用功能

#### 支持的插件下载
- **CamelCrusher**: 失真效果插件
- **TAL-Reverb-4-64**: 混响效果插件
- **TSE_808_2.0_x64**: 808失真效果插件

#### 状态指示
- **❌ 未安装**: 插件文件不存在，显示下载按钮
- **✅ 已安装**: 插件文件存在，隐藏下载按钮

### 🔄 自动重新加载功能

系统具备智能的插件重新加载机制：

#### 触发时机
1. **启用功能时**: 检查所有插件文件是否存在
2. **激活插件时**: 如果插件激活失败，自动尝试重新加载

#### 重新加载过程
1. **检测问题**: 发现插件文件不存在或激活失败
2. **删除旧滤镜**: 如果存在旧的滤镜，先删除
3. **等待清理**: 等待1秒确保完全清理
4. **检查文件**: 再次检查插件文件是否存在
5. **重新创建**: 使用相同配置重新创建VST滤镜
6. **重新激活**: 尝试重新激活插件

#### 日志输出示例
```
🔄 开始重新加载插件滤镜: CamelCrusher_去重
删除现有滤镜: CamelCrusher_去重
✅ 成功删除现有滤镜: CamelCrusher_去重
重新创建VST滤镜: CamelCrusher_去重
✅ 成功重新创建VST滤镜: CamelCrusher_去重
插件重新加载成功，重新尝试激活: CamelCrusher 失真效果
```

### 故障排除
- 如果插件无法激活，系统会自动尝试重新加载
- 如果重新加载失败，检查插件文件路径是否正确
- 如果功能无法启动，确保已连接OBS并选择了音频源
- 查看控制台输出获取详细的错误信息和重新加载状态

## 🎵 效果说明

通过独立管理3个VST插件，可以实现：
- **音频特征变化**: 不同插件产生不同的音频效果
- **避免重复检测**: 持续变化的音频特征降低被检测的概率
- **独立控制**: 每个插件独立运行，可以同时或错开启用
- **自动化管理**: 无需手动切换，系统自动管理每个插件
- **灵活配置**: 每个插件都可以独立调整时间参数
- **智能加载**: 自动检测和加载插件文件

这个功能与其他音频去重功能（如EQ去重、断音控制等）可以同时使用，形成多层次的音频变化策略。每个插件的独立运行机制确保了更丰富的音频效果组合。
