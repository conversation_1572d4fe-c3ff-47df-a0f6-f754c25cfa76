# 🎛️ OBS VST滤镜管理器使用说明

## 📋 功能概述

这个Python程序可以连接到OBS Studio，实现以下功能：

1. **连接OBS**: 通过WebSocket连接到OBS Studio
2. **获取媒体源**: 自动获取并显示所有媒体源
3. **管理滤镜**: 查看选中媒体源的所有滤镜
4. **添加VST滤镜**: 支持添加你的三个VST插件作为滤镜

## 🔧 支持的VST插件

程序预配置了你的三个VST插件：

### 1. Auburn Sounds Graillon 3-64
- **功能**: 音调变声器
- **路径**: `C:\Program Files\VstPlugins\Auburn Sounds\Graillon 3-64.dll`
- **用途**: 实现变声和音调变化效果

### 2. TSE_808_2.0_x64  
- **功能**: 失真效果器
- **路径**: `C:\Program Files\VstPlugins\TSE_808_2.0_x64.dll`
- **用途**: 增加温暖的失真和过载效果

### 3. TAL-Reverb-4-64
- **功能**: 混响效果器
- **路径**: `C:\Program Files\VstPlugins\TAL-Reverb-4-64.dll`
- **用途**: 添加空间混响效果

## 🚀 使用步骤

### 第一步：准备环境

1. **确保OBS Studio已安装并运行**
2. **启用OBS WebSocket服务器**：
   - 在OBS中：工具 → WebSocket服务器设置
   - 勾选"启用WebSocket服务器"
   - 端口设为4455（默认）
   - 建议取消勾选"启用身份验证"（简化连接）

3. **确保VST插件已安装**：
   - 检查插件文件是否存在于指定路径
   - 如果路径不同，需要修改程序中的路径配置

### 第二步：运行程序

1. **启动程序**：
   ```bash
   python obs_vst_manager.py
   ```
   或者运行测试脚本：
   ```bash
   python test_vst_manager.py
   ```

2. **连接OBS**：
   - 确认WebSocket地址为 `ws://localhost:4455`
   - 点击"连接OBS"按钮
   - 等待状态显示"已连接"

### 第三步：管理媒体源和滤镜

1. **查看媒体源**：
   - 连接成功后会自动刷新媒体源列表
   - 在下拉框中选择要管理的媒体源
   - 可以点击"刷新媒体源"手动更新

2. **查看滤镜**：
   - 选择媒体源后会自动显示该源的所有滤镜
   - 滤镜列表显示：名称、类型、启用状态
   - VST滤镜会特别标记为🎛️

### 第四步：添加VST滤镜

1. **选择VST插件**：
   - 在"添加VST滤镜"区域选择要添加的插件
   - 三个选项：Auburn Sounds Graillon 3-64、TSE_808_2.0_x64、TAL-Reverb-4-64

2. **设置滤镜名称**：
   - 输入自定义的滤镜名称（如"我的变声器"）
   - 名称不能与现有滤镜重复

3. **添加滤镜**：
   - 点击"添加VST滤镜"按钮
   - 程序会自动检查插件文件是否存在
   - 如果滤镜已存在，会先删除再重新创建

## 📊 界面说明

### 连接区域
- **WebSocket地址**: OBS WebSocket服务器地址
- **连接按钮**: 连接/断开OBS
- **状态显示**: 显示连接状态（已连接/未连接/连接失败）

### 媒体源管理
- **媒体源下拉框**: 显示所有可用的媒体源
- **刷新按钮**: 手动刷新媒体源列表

### 滤镜管理
- **滤镜列表**: 表格形式显示所有滤镜
  - 滤镜名称：显示滤镜的名称
  - 滤镜类型：显示滤镜的技术类型
  - 状态：显示是否启用（✅启用/❌禁用）

### VST滤镜添加
- **插件选择**: 下拉框选择要添加的VST插件
- **滤镜名称**: 输入框设置滤镜的显示名称
- **添加按钮**: 执行添加操作

### 操作日志
- 显示所有操作的详细日志
- 包括连接状态、操作结果、错误信息等
- 带时间戳，方便调试

## ⚠️ 注意事项

### 1. 插件路径配置
如果你的VST插件安装在不同路径，需要修改程序中的配置：

```python
self.vst_plugins = {
    "Auburn Sounds Graillon 3-64": {
        "plugin_path": r"你的实际路径\Graillon 3-64.dll"
    },
    # ... 其他插件
}
```

### 2. OBS WebSocket设置
- 确保OBS WebSocket服务器已启用
- 端口号必须匹配（默认4455）
- 建议禁用身份验证以简化连接

### 3. 媒体源类型
- 程序会自动识别不同类型的媒体源
- 🎬 媒体源：视频文件、流媒体等
- 🎵 音频源：麦克风、音频设备等
- 📹 视频源：摄像头、采集卡等
- ⚪ 其他源：文本、图片等

### 4. VST滤镜特点
- VST滤镜添加后需要时间加载
- 某些VST插件可能需要手动打开界面进行初始化
- 滤镜名称不能重复，重复时会自动删除旧的

## 🔧 故障排除

### 连接失败
**可能原因**：
- OBS未启动
- WebSocket服务器未启用
- 端口号不匹配
- 防火墙阻止连接

**解决方法**：
1. 确认OBS已启动
2. 检查WebSocket服务器设置
3. 尝试重启OBS和程序
4. 检查防火墙设置

### 插件添加失败
**可能原因**：
- 插件文件不存在
- 插件路径错误
- 插件不兼容
- 权限不足

**解决方法**：
1. 检查插件文件是否存在
2. 确认插件路径正确
3. 尝试以管理员身份运行
4. 检查插件是否为64位版本

### 媒体源不显示
**可能原因**：
- OBS中没有媒体源
- 连接断开
- 权限问题

**解决方法**：
1. 在OBS中添加媒体源
2. 重新连接OBS
3. 点击刷新按钮

## 📞 技术支持

如果遇到问题，请检查：

1. **Python环境**: 确保Python 3.7+已安装
2. **依赖库**: 确保websocket-client库已安装
3. **OBS版本**: 建议使用OBS Studio 28.0+
4. **系统权限**: 某些操作可能需要管理员权限

---

**版本**: v1.0  
**最后更新**: 2025-07-28
