# OBS去重工具打包脚本
Write-Host "🚀 OBS去重工具打包" -ForegroundColor Green
Write-Host "==========================================" -ForegroundColor Green

# 清理旧文件
Write-Host "🧹 清理旧文件..." -ForegroundColor Yellow
if (Test-Path "build") { Remove-Item -Recurse -Force "build" }
if (Test-Path "dist") { Remove-Item -Recurse -Force "dist" }
if (Test-Path "__pycache__") { Remove-Item -Recurse -Force "__pycache__" }

# 检查必要文件
$requiredFiles = @("main.py", "main_module.py", "obs2.ico", "zuozhe.png")
Write-Host "📋 检查必要文件..." -ForegroundColor Yellow
foreach ($file in $requiredFiles) {
    if (Test-Path $file) {
        Write-Host "   ✅ $file" -ForegroundColor Green
    } else {
        Write-Host "   ❌ $file - 缺失!" -ForegroundColor Red
        exit 1
    }
}

# 构建命令
Write-Host "🔨 开始打包..." -ForegroundColor Yellow
$cmd = @(
    "pyinstaller",
    "--onefile",
    "--windowed", 
    "--icon=obs2.ico",
    "--name=OBS去重工具",
    "--clean",
    "--noconfirm",
    "--add-data", "obs2.ico;.",
    "--add-data", "obs3.ico;.",
    "--add-data", "zuozhe.png;.",
    "--add-data", "platforms;platforms",
    "--add-data", "saved_colors;saved_colors",
    "--add-data", "压缩.lua;.",
    "--add-data", "增益.lua;.",
    "--add-data", "随机音频播放大小.lua;.",
    "--add-data", "插件去重功能说明.md;.",
    "--hidden-import", "PyQt5.QtCore",
    "--hidden-import", "PyQt5.QtGui",
    "--hidden-import", "PyQt5.QtWidgets",
    "--hidden-import", "websockets",
    "--hidden-import", "websocket",
    "--hidden-import", "asyncio",
    "--hidden-import", "webbrowser",
    "main.py"
)

# 执行打包
try {
    & $cmd[0] $cmd[1..($cmd.Length-1)]
    
    # 检查结果
    if (Test-Path "dist\OBS去重工具.exe") {
        $size = (Get-Item "dist\OBS去重工具.exe").Length / 1MB
        Write-Host ""
        Write-Host "🎉 打包成功！" -ForegroundColor Green
        Write-Host "📁 文件位置: dist\OBS去重工具.exe" -ForegroundColor Green
        Write-Host "📏 文件大小: $([math]::Round($size, 1)) MB" -ForegroundColor Green
        Write-Host "🖼️ 已包含图标和资源文件" -ForegroundColor Green
    } else {
        Write-Host "❌ 打包失败 - 未找到生成的exe文件" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ 打包过程中出现错误: $_" -ForegroundColor Red
}

Write-Host ""
Write-Host "打包完成" -ForegroundColor Green
Read-Host "按回车键退出"
