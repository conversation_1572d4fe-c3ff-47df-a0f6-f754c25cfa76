#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终打包脚本 - 跳过PYD转换
"""

import os
import sys
import subprocess
import shutil

def main():
    """主函数"""
    print("🚀 OBS去重工具最终打包")
    print("=" * 50)
    print("ℹ️ 由于main_module.py文件较大(10000+行)，跳过PYD转换")
    print("ℹ️ 直接使用PyInstaller打包原始Python文件")
    print()
    
    # 清理旧文件
    print("🧹 清理旧文件...")
    for item in ["build", "dist", "__pycache__"]:
        if os.path.exists(item):
            if os.path.isdir(item):
                shutil.rmtree(item)
            else:
                os.remove(item)
            print(f"   删除: {item}")
    
    # 检查必要文件
    required_files = ["main.py", "main_module.py", "obs2.ico", "zuozhe.png"]
    print("\n📋 检查必要文件...")
    for file in required_files:
        if os.path.exists(file):
            print(f"   ✅ {file}")
        else:
            print(f"   ❌ {file} - 缺失!")
            return False
    
    # PyInstaller命令
    cmd = [
        "pyinstaller",
        "--onefile",                    # 单文件
        "--windowed",                   # 无控制台
        "--icon=obs2.ico",             # 图标
        "--name=OBS去重工具",           # 名称
        "--clean",                      # 清理
        "--noconfirm",                 # 不确认
        
        # 数据文件
        "--add-data", "obs2.ico;.",
        "--add-data", "obs3.ico;.", 
        "--add-data", "zuozhe.png;.",
        "--add-data", "platforms;platforms",
        "--add-data", "saved_colors;saved_colors",
        "--add-data", "压缩.lua;.",
        "--add-data", "增益.lua;.",
        "--add-data", "随机音频播放大小.lua;.",
        "--add-data", "插件去重功能说明.md;.",
        
        # 隐藏导入
        "--hidden-import", "PyQt5.QtCore",
        "--hidden-import", "PyQt5.QtGui",
        "--hidden-import", "PyQt5.QtWidgets", 
        "--hidden-import", "websockets",
        "--hidden-import", "websocket",
        "--hidden-import", "asyncio",
        "--hidden-import", "webbrowser",
        "--hidden-import", "json",
        "--hidden-import", "random",
        "--hidden-import", "time",
        "--hidden-import", "hmac",
        "--hidden-import", "hashlib",
        "--hidden-import", "base64",
        
        "main.py"
    ]
    
    print("\n🔨 开始打包...")
    print("💻 这可能需要几分钟时间，请耐心等待...")
    
    try:
        # 执行打包
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        
        # 检查结果
        exe_path = "dist/OBS去重工具.exe"
        if os.path.exists(exe_path):
            size_mb = os.path.getsize(exe_path) / (1024 * 1024)
            print(f"\n🎉 打包成功！")
            print(f"📁 文件位置: {exe_path}")
            print(f"📏 文件大小: {size_mb:.1f} MB")
            print(f"🖼️ 程序图标: obs2.ico (任务栏和左上角)")
            print(f"🖼️ 内置资源: zuozhe.png (作者二维码)")
            print(f"📦 打包方式: 单文件 (无需额外依赖)")
            
            print(f"\n✨ 打包完成！可以分发 {exe_path} 文件")
            return True
        else:
            print("❌ 未找到生成的exe文件")
            return False
            
    except subprocess.CalledProcessError as e:
        print(f"❌ 打包失败")
        print(f"返回码: {e.returncode}")
        if e.stdout:
            print(f"输出: {e.stdout}")
        if e.stderr:
            print(f"错误: {e.stderr}")
        return False
    except Exception as e:
        print(f"❌ 异常: {e}")
        return False

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n🎊 恭喜！OBS去重工具打包成功！")
        else:
            print("\n💥 打包失败，请检查错误信息")
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断")
    except Exception as e:
        print(f"\n❌ 未知错误: {e}")
    
    input("\n按回车键退出...")
