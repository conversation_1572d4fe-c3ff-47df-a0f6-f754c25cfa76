#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安装OBS去重工具打包所需的依赖
"""

import subprocess
import sys
import os

def run_pip_command(command, description):
    """运行pip命令"""
    print(f"🔧 {description}")
    print(f"💻 {command}")
    
    try:
        result = subprocess.run(command, shell=True, check=True, 
                              capture_output=True, text=True, encoding='utf-8')
        print(f"✅ {description} 成功")
        if result.stdout:
            print(f"输出: {result.stdout.strip()}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} 失败")
        print(f"错误代码: {e.returncode}")
        if e.stdout:
            print(f"标准输出: {e.stdout.strip()}")
        if e.stderr:
            print(f"错误输出: {e.stderr.strip()}")
        return False

def check_python_version():
    """检查Python版本"""
    print("🐍 检查Python版本...")
    version = sys.version_info
    print(f"Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 7):
        print("⚠️ 建议使用Python 3.7或更高版本")
        return False
    else:
        print("✅ Python版本符合要求")
        return True

def upgrade_pip():
    """升级pip"""
    print("\n📦 升级pip...")
    return run_pip_command(
        f"{sys.executable} -m pip install --upgrade pip",
        "升级pip到最新版本"
    )

def install_basic_dependencies():
    """安装基础依赖"""
    print("\n📚 安装基础依赖...")
    
    dependencies = [
        "PyQt5",
        "websockets", 
        "websocket-client",
        "asyncio",
    ]
    
    success_count = 0
    for dep in dependencies:
        if run_pip_command(
            f"{sys.executable} -m pip install {dep}",
            f"安装 {dep}"
        ):
            success_count += 1
        print()  # 空行分隔
    
    print(f"📊 基础依赖安装结果: {success_count}/{len(dependencies)} 成功")
    return success_count == len(dependencies)

def install_build_dependencies():
    """安装打包依赖"""
    print("\n🔨 安装打包依赖...")
    
    build_deps = [
        "PyInstaller",
        "Cython",
    ]
    
    success_count = 0
    for dep in build_deps:
        if run_pip_command(
            f"{sys.executable} -m pip install {dep}",
            f"安装 {dep}"
        ):
            success_count += 1
        print()  # 空行分隔
    
    print(f"📊 打包依赖安装结果: {success_count}/{len(build_deps)} 成功")
    return success_count == len(build_deps)

def verify_installations():
    """验证安装结果"""
    print("\n🔍 验证安装结果...")
    
    modules_to_check = [
        ("PyQt5", "PyQt5"),
        ("websockets", "websockets"),
        ("websocket", "websocket-client"),
        ("PyInstaller", "PyInstaller"),
        ("Cython", "Cython"),
    ]
    
    success_count = 0
    for module, package_name in modules_to_check:
        try:
            __import__(module)
            print(f"✅ {package_name} - 安装成功")
            success_count += 1
        except ImportError:
            print(f"❌ {package_name} - 安装失败或无法导入")
    
    print(f"\n📊 验证结果: {success_count}/{len(modules_to_check)} 模块可用")
    return success_count == len(modules_to_check)

def show_versions():
    """显示已安装包的版本"""
    print("\n📋 已安装包版本信息:")
    
    packages = ["PyQt5", "PyInstaller", "Cython", "websockets"]
    
    for package in packages:
        try:
            result = subprocess.run(
                f"{sys.executable} -m pip show {package}",
                shell=True, capture_output=True, text=True, encoding='utf-8'
            )
            if result.returncode == 0:
                lines = result.stdout.split('\n')
                for line in lines:
                    if line.startswith('Version:'):
                        version = line.split(':', 1)[1].strip()
                        print(f"   {package}: {version}")
                        break
            else:
                print(f"   {package}: 未安装")
        except Exception as e:
            print(f"   {package}: 检查失败 ({e})")

def main():
    """主函数"""
    print("🚀 OBS去重工具依赖安装脚本")
    print("=" * 50)
    
    try:
        # 1. 检查Python版本
        if not check_python_version():
            print("⚠️ Python版本可能不兼容，但继续安装...")
        
        # 2. 升级pip
        if not upgrade_pip():
            print("⚠️ pip升级失败，但继续安装...")
        
        # 3. 安装基础依赖
        basic_success = install_basic_dependencies()
        
        # 4. 安装打包依赖
        build_success = install_build_dependencies()
        
        # 5. 验证安装
        verify_success = verify_installations()
        
        # 6. 显示版本信息
        show_versions()
        
        # 总结
        print("\n" + "=" * 50)
        if verify_success:
            print("🎉 所有依赖安装成功！")
            print("✨ 现在可以运行打包脚本了:")
            print("   - 快速打包: python quick_build.py")
            print("   - 完整打包: python build_script.py")
            print("   - 转换PYD: python convert_to_pyd.py")
        else:
            print("⚠️ 部分依赖安装失败")
            print("💡 建议:")
            print("   1. 检查网络连接")
            print("   2. 尝试使用国内镜像源:")
            print("      pip install -i https://pypi.tuna.tsinghua.edu.cn/simple/ <包名>")
            print("   3. 手动安装失败的包")
        
        return verify_success
        
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断了安装过程")
        return False
    except Exception as e:
        print(f"\n❌ 安装过程中出现异常: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    input("\n按回车键退出...")
    sys.exit(0 if success else 1)
