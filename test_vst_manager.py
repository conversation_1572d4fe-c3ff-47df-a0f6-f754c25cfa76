#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试OBS VST滤镜管理器
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from obs_vst_manager import OBSVSTManager

def main():
    """主函数"""
    print("🚀 启动OBS VST滤镜管理器测试...")
    
    try:
        # 创建管理器实例
        manager = OBSVSTManager()
        
        # 运行GUI
        manager.run()
        
    except Exception as e:
        print(f"❌ 程序运行出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
