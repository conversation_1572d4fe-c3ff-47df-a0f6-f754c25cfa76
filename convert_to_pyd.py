#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
将Python文件转换为PYD格式的脚本
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

def create_setup_file():
    """创建Cython setup文件"""
    setup_content = '''
from setuptools import setup
from Cython.Build import cythonize

# 编译器指令
compiler_directives = {
    'language_level': 3,
    'boundscheck': False,
    'wraparound': False,
    'initializedcheck': False,
    'cdivision': True,
    'embedsignature': True,
}

# 要编译的文件
ext_modules = cythonize([
    "main_module.py",
], compiler_directives=compiler_directives)

setup(
    ext_modules=ext_modules,
    zip_safe=False,
)
'''
    
    with open("setup_pyd.py", "w", encoding="utf-8") as f:
        f.write(setup_content)
    
    print("✅ 创建了 setup_pyd.py")

def convert_to_pyd():
    """转换为PYD"""
    print("🔄 开始转换Python文件为PYD...")
    
    try:
        # 检查Cython是否安装
        import Cython
        print(f"✅ Cython版本: {Cython.__version__}")
    except ImportError:
        print("❌ Cython未安装，请先安装: pip install Cython")
        return False
    
    # 创建setup文件
    create_setup_file()
    
    # 执行编译
    cmd = "python setup_pyd.py build_ext --inplace"
    print(f"🔧 执行命令: {cmd}")
    
    try:
        result = subprocess.run(cmd, shell=True, check=True,
                              capture_output=True, text=True, encoding='utf-8', errors='replace')
        print("✅ 编译成功")
        if result.stdout:
            print(f"编译输出: {result.stdout}")
        
        # 检查生成的PYD文件
        pyd_files = list(Path(".").glob("*.pyd"))
        if pyd_files:
            print("📁 生成的PYD文件:")
            for pyd in pyd_files:
                size = pyd.stat().st_size / 1024  # KB
                print(f"   - {pyd} ({size:.1f} KB)")
        else:
            print("⚠️ 未找到PYD文件")
        
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 编译失败: {e}")
        if e.stdout:
            print(f"输出: {e.stdout}")
        if e.stderr:
            print(f"错误: {e.stderr}")
        return False

def cleanup():
    """清理临时文件"""
    print("🧹 清理临时文件...")
    
    cleanup_items = [
        "build",
        "setup_pyd.py",
        "*.c",
    ]
    
    for item in cleanup_items:
        if "*" in item:
            files = list(Path(".").glob(item))
            for file in files:
                os.remove(file)
                print(f"   删除: {file}")
        else:
            if os.path.exists(item):
                if os.path.isdir(item):
                    shutil.rmtree(item)
                else:
                    os.remove(item)
                print(f"   删除: {item}")

def main():
    """主函数"""
    print("🔄 Python转PYD工具")
    print("=" * 40)
    
    try:
        # 检查源文件
        if not os.path.exists("main_module.py"):
            print("❌ 找不到 main_module.py")
            return False
        
        print("📋 源文件:")
        print("   - main_module.py")
        
        # 转换
        if convert_to_pyd():
            print("\n🎉 转换成功！")
            print("💡 现在可以使用生成的PYD文件进行打包")
            
            # 询问是否清理
            clean = input("\n是否清理临时文件？(Y/n): ").lower().strip()
            if clean != 'n':
                cleanup()
            
            return True
        else:
            print("\n💥 转换失败")
            return False
            
    except Exception as e:
        print(f"❌ 异常: {e}")
        return False

if __name__ == "__main__":
    try:
        success = main()
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断")
        success = False
    
    input("\n按回车键退出...")
    sys.exit(0 if success else 1)
