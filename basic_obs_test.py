#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基础OBS连接测试 - 不依赖外部库的版本
"""

import json
import socket
import time
import base64
import hashlib
import struct

def create_websocket_key():
    """生成WebSocket密钥"""
    import random
    key = base64.b64encode(bytes([random.randint(0, 255) for _ in range(16)])).decode()
    return key

def websocket_handshake(host, port):
    """执行WebSocket握手"""
    try:
        # 创建socket连接
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)
        sock.connect((host, port))
        
        # 生成WebSocket密钥
        key = create_websocket_key()
        
        # 构建HTTP握手请求
        request = (
            f"GET / HTTP/1.1\r\n"
            f"Host: {host}:{port}\r\n"
            f"Upgrade: websocket\r\n"
            f"Connection: Upgrade\r\n"
            f"Sec-WebSocket-Key: {key}\r\n"
            f"Sec-WebSocket-Version: 13\r\n"
            f"\r\n"
        ).encode()
        
        sock.send(request)
        
        # 接收响应
        response = sock.recv(1024).decode()
        
        if "101 Switching Protocols" in response:
            print("✅ WebSocket握手成功")
            return sock
        else:
            print(f"❌ WebSocket握手失败: {response}")
            sock.close()
            return None
            
    except Exception as e:
        print(f"❌ 连接失败: {e}")
        return None

def send_websocket_frame(sock, data):
    """发送WebSocket帧"""
    try:
        payload = data.encode() if isinstance(data, str) else data
        payload_len = len(payload)
        
        # 构建帧头
        frame = bytearray()
        frame.append(0x81)  # FIN=1, opcode=1 (text)
        
        if payload_len < 126:
            frame.append(0x80 | payload_len)  # MASK=1, payload length
        elif payload_len < 65536:
            frame.append(0x80 | 126)
            frame.extend(struct.pack('>H', payload_len))
        else:
            frame.append(0x80 | 127)
            frame.extend(struct.pack('>Q', payload_len))
        
        # 添加掩码
        mask = bytes([0x12, 0x34, 0x56, 0x78])  # 简单的固定掩码
        frame.extend(mask)
        
        # 掩码处理payload
        masked_payload = bytearray()
        for i, byte in enumerate(payload):
            masked_payload.append(byte ^ mask[i % 4])
        
        frame.extend(masked_payload)
        
        sock.send(frame)
        return True
        
    except Exception as e:
        print(f"❌ 发送帧失败: {e}")
        return False

def receive_websocket_frame(sock):
    """接收WebSocket帧"""
    try:
        # 读取帧头
        header = sock.recv(2)
        if len(header) < 2:
            return None
            
        fin = (header[0] & 0x80) != 0
        opcode = header[0] & 0x0f
        masked = (header[1] & 0x80) != 0
        payload_len = header[1] & 0x7f
        
        # 读取扩展长度
        if payload_len == 126:
            extended_len = sock.recv(2)
            payload_len = struct.unpack('>H', extended_len)[0]
        elif payload_len == 127:
            extended_len = sock.recv(8)
            payload_len = struct.unpack('>Q', extended_len)[0]
        
        # 读取掩码（如果有）
        if masked:
            mask = sock.recv(4)
        
        # 读取payload
        payload = sock.recv(payload_len)
        
        # 解掩码
        if masked:
            unmasked = bytearray()
            for i, byte in enumerate(payload):
                unmasked.append(byte ^ mask[i % 4])
            payload = bytes(unmasked)
        
        return payload.decode() if opcode == 1 else payload
        
    except Exception as e:
        print(f"❌ 接收帧失败: {e}")
        return None

def test_obs_connection():
    """测试OBS连接"""
    print("🔄 开始测试OBS WebSocket连接...")
    
    # 连接参数
    host = "localhost"
    port = 4455
    
    # 执行WebSocket握手
    sock = websocket_handshake(host, port)
    if not sock:
        return False
    
    try:
        # 接收Hello消息
        print("📨 等待Hello消息...")
        hello_data = receive_websocket_frame(sock)
        if hello_data:
            hello_json = json.loads(hello_data)
            print(f"✅ 收到Hello消息: {hello_json.get('d', {}).get('obsWebSocketVersion', 'Unknown')}")
            
            # 发送Identify消息
            identify_payload = {
                "op": 1,
                "d": {
                    "rpcVersion": hello_json.get("d", {}).get("rpcVersion", 1),
                    "eventSubscriptions": 0
                }
            }
            
            print("📤 发送Identify消息...")
            if send_websocket_frame(sock, json.dumps(identify_payload)):
                # 接收Identified消息
                identified_data = receive_websocket_frame(sock)
                if identified_data:
                    identified_json = json.loads(identified_data)
                    if identified_json.get("op") == 2:
                        print("🎉 OBS连接建立成功！")
                        
                        # 测试获取输入源列表
                        print("🔍 测试获取输入源列表...")
                        request_payload = {
                            "op": 6,
                            "d": {
                                "requestType": "GetInputList",
                                "requestId": "test-request-1",
                                "requestData": {}
                            }
                        }
                        
                        if send_websocket_frame(sock, json.dumps(request_payload)):
                            response_data = receive_websocket_frame(sock)
                            if response_data:
                                response_json = json.loads(response_data)
                                if response_json.get("op") == 7:
                                    inputs = response_json.get("d", {}).get("responseData", {}).get("inputs", [])
                                    print(f"✅ 成功获取到 {len(inputs)} 个输入源")
                                    
                                    for i, inp in enumerate(inputs[:5], 1):  # 只显示前5个
                                        name = inp.get('inputName', '')
                                        kind = inp.get('inputKind', '')
                                        print(f"  {i}. {name} ({kind})")
                                    
                                    if len(inputs) > 5:
                                        print(f"  ... 还有 {len(inputs) - 5} 个输入源")
                                        
                                    return True
                        
        print("❌ 连接测试失败")
        return False
        
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        return False
        
    finally:
        sock.close()

def main():
    """主函数"""
    print("🎛️ OBS WebSocket连接测试工具")
    print("=" * 50)
    
    print("📋 测试说明:")
    print("1. 确保OBS Studio正在运行")
    print("2. 确保WebSocket服务器已启用（端口4455）")
    print("3. 建议禁用身份验证以简化连接")
    print("=" * 50)
    
    # 执行连接测试
    success = test_obs_connection()
    
    print("=" * 50)
    if success:
        print("🎉 测试完成！OBS连接正常")
        print("💡 现在可以尝试运行完整的VST管理器")
    else:
        print("❌ 测试失败！请检查:")
        print("   - OBS是否正在运行")
        print("   - WebSocket服务器是否启用")
        print("   - 端口4455是否被占用")
        print("   - 防火墙是否阻止连接")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
